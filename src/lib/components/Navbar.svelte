<script lang="ts">
  import { push } from "svelte-spa-router";
  import { Menu } from "lucide-svelte";
  
  let isMenuOpen = $state(false);
  
  function toggleMenu() {
    isMenuOpen = !isMenuOpen;
  }
  
  function navigateTo(path: string) {
    isMenuOpen = false;
    push(path);
  }
</script>

<header class="fixed top-0 z-50 w-full border-b bg-background backdrop-blur supports-[backdrop-filter]:bg-background/50 shadow-md">
  <div class="container flex h-16 items-center">
    <div class="flex items-center justify-between w-full px-4">
      <button class="h-10" onclick={() => push("/")}>
        <img src="assets/logo.png" alt="Logo" class="h-full" />
      </button>
      
      <!-- Hamburger Button -->
      <div class="relative">
        <button 
          class="p-2 hover:bg-accent rounded-md"
          onclick={toggleMenu}
          aria-label="Toggle menu"
          aria-expanded={isMenuOpen}
        >
          <Menu size={24} />
        </button>

        <!-- Mobile Menu -->
        {#if isMenuOpen}
          <nav class="mobile-menu">
            <ul>
              <li>
                <button onclick={() => navigateTo("/terms")}>Termini e Condizioni</button>
              </li>
              <li>
                <button onclick={() => navigateTo("/privacy")}>Privacy policy</button>
              </li>
              <li>
                <button onclick={() => navigateTo("/contacts")}>Contatti</button>
              </li>
            </ul>
          </nav>
        {/if}
      </div>
    </div>
  </div>
</header>

<div class="h-16"></div>

<style>
  .mobile-menu {
    position: absolute;
    top: calc(100% + 0.5rem);
    right: 0;
    min-width: 200px;
    background: var(--background);
    padding: 0.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    z-index: 49;
    border: 1px solid var(--border);
  }

  .mobile-menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .mobile-menu li:not(:last-child) {
    margin-bottom: 0.5rem;
  }

  .mobile-menu button {
    width: 100%;
    text-align: left;
    padding: 0.5rem;
    background: transparent;
    border: none;
    color: inherit;
    font-size: 1rem;
    cursor: pointer;
    border-radius: 0.25rem;
  }

  .mobile-menu button:hover {
    background: var(--accent);
  }
</style>