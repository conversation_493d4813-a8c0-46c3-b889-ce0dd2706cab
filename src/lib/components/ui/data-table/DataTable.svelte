<script lang="ts">
  import { Button } from "$lib/components/ui/button";
  import { Input } from "$lib/components/ui/input";
  import { Plus, Search } from "lucide-svelte";
  import type { Snippet } from 'svelte';
  
  let { title, description, onAdd, searchPlaceholder = "Cerca...", children, pagination, onSearch, actions } = $props<{
    title: string;
    description: string;
    onAdd: () => void;
    searchPlaceholder?: string;
    children: Snippet;
    pagination?: Snippet;
    onSearch?: (event: Event) => void;
    actions?: Snippet;
  }>();
  
  let searchValue = $state("");
</script>

<div class="space-y-4">
  <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
    <div>
      <h2 class="text-2xl font-bold tracking-tight">{title}</h2>
      <p class="text-muted-foreground">{description}</p>
    </div>
    <Button onclick={onAdd}>
      <Plus class="mr-2" size={16} />
      Aggiungi
    </Button>
  </div>

  <div class="flex flex-col gap-2">
    <div class="relative flex-1">
      <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
      <Input
        placeholder={searchPlaceholder}
        oninput={onSearch}
        class="pl-8"
      />
    </div>
    <div class="md:hidden">
      {@render actions?.()}
    </div>
  </div>

  <div class="rounded-md border">
    {@render children()}
  </div>

  {@render pagination?.()}
</div> 