<script lang="ts">
  import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogDescription, DialogFooter } from "$lib/components/ui/dialog";
  import { Button } from "$lib/components/ui/button";
  import { Input } from "$lib/components/ui/input";
  import { Label } from "$lib/components/ui/label";

  type Field = {
    key: string;
    label: string;
    type?: string;
    required?: boolean;
    value: any;
    options?: { value: any; label: string | null }[];
    min?: number;
    max?: number;
    step?: number;
    pattern?: string;
  };

  let {
    open = false,
    title,
    description,
    fields,
    onSubmit,
    onCancel,
    loading = false
  } = $props<{
    open: boolean;
    title: string;
    description: string;
    fields: Field[];
    onSubmit: (data: Record<string, any>) => void;
    onCancel: () => void;
    loading?: boolean;
  }>();

  let formData = $state<Record<string, any>>({});
  let errors = $state<Record<string, string>>({});

  $effect(() => {
    if (fields) {
      formData = Object.fromEntries(
        fields.map((field: Field) => {
          if (field.type === 'number') {
            return [field.key, field.value !== undefined && field.value !== null ? Number(field.value) : field.value];
          }
          return [field.key, field.type === 'checkbox' ? !!field.value : field.value];
        })
      );
    }
  });

  function validateField(field: Field, value: any): string {
    if (field.type === 'number' && value !== null && value !== '') {
      const numValue = Number(value);
      if (isNaN(numValue)) {
        return 'Inserisci un numero valido';
      }
      if (field.min !== undefined && numValue < field.min) {
        return `Il valore minimo è ${field.min}`;
      }
      if (field.max !== undefined && numValue > field.max) {
        return `Il valore massimo è ${field.max}`;
      }
    }
    return '';
  }

  function handleInput(field: Field, value: any) {
    // For number fields, convert empty string to null
    if (field.type === 'number' && value === '') {
      formData[field.key] = null;
    } else {
      formData[field.key] = value;
    }

    const error = validateField(field, value);
    if (error) {
      errors[field.key] = error;
    } else {
      delete errors[field.key];
    }
  }

  function handleSubmit(e: SubmitEvent) {
    e.preventDefault();

    // Validate all fields before submitting
    let hasErrors = false;
    fields.forEach((field: Field) => {
      const error = validateField(field, formData[field.key]);
      if (error) {
        errors[field.key] = error;
        hasErrors = true;
      }
    });

    if (!hasErrors) {
      onSubmit(formData);
    }
  }

  function handleOpenChange(isOpen: boolean) {
    if (!isOpen) {
      onCancel();
    }
  }
</script>

<Dialog {open} onOpenChange={handleOpenChange}>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>{title}</DialogTitle>
      <DialogDescription>{description}</DialogDescription>
    </DialogHeader>

    <form onsubmit={handleSubmit} class="space-y-4">
      {#each fields as field}
        <div class="space-y-2">
          <Label for={field.key}>{field.label}</Label>
          {#if field.type === "checkbox"}
            <div class="flex items-center space-x-2">
              <input
                type="checkbox"
                id={field.key}
                required={field.required}
                bind:checked={formData[field.key]}
                class="h-4 w-4 rounded border-input bg-background"
              />
            </div>
          {:else if field.type === "number"}
            <Input
              type="number"
              id={field.key}
              required={field.required}
              min={field.min}
              max={field.max}
              step={field.step || "any"}
              value={formData[field.key]}
              oninput={(e) => handleInput(field, e.currentTarget.value)}
              class={errors[field.key] ? 'border-red-500' : ''}
            />
            {#if errors[field.key]}
              <p class="text-sm text-red-500">{errors[field.key]}</p>
            {/if}
          {:else}
            <Input
              type={field.type || "text"}
              id={field.key}
              required={field.required}
              pattern={field.pattern}
              bind:value={formData[field.key]}
            />
          {/if}
        </div>
      {/each}

      <DialogFooter>
        <Button type="button" variant="outline" onclick={onCancel}>
          Annulla
        </Button>
        <Button type="submit" disabled={loading || Object.keys(errors).length > 0}>
          {loading ? "Salvataggio..." : "Salva"}
        </Button>
      </DialogFooter>
    </form>
  </DialogContent>
</Dialog>