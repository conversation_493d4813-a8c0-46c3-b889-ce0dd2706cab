import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { authService } from "./supabase/auth";

export function cn(...inputs: ClassValue[]) {
 return twMerge(clsx(inputs));
}

export async function isAdminLogged() {
  const user = await authService.getCurrentUser();
  return user !== null && user?.role === "service_role";
}

/**
 * Gets the email of the currently logged in user
 * @returns Promise with the user's email or null if not logged in
 */
export async function getCurrentUserEmail(): Promise<string | null> {
  const user = await authService.getCurrentUser();
  return user?.email || null;
}
