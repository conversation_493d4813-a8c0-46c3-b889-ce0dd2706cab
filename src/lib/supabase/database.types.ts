export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      annunci: {
        Row: {
          comune_id: number | null
          fine_validità: string | null
          id: number
          inizio_validità: string | null
          messaggio: string | null
        }
        Insert: {
          comune_id?: number | null
          fine_validità?: string | null
          id?: number
          inizio_validità?: string | null
          messaggio?: string | null
        }
        Update: {
          comune_id?: number | null
          fine_validità?: string | null
          id?: number
          inizio_validità?: string | null
          messaggio?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "annunci_comune_id_fkey"
            columns: ["comune_id"]
            isOneToOne: false
            referencedRelation: "comuni"
            referencedColumns: ["id"]
          },
        ]
      }
      artisti: {
        Row: {
          biografia: string | null
          categoria: string | null
          categoria_artisti_id: number | null
          creato_da: string | null
          creato_il: string | null
          data_nascita: string | null
          email: string | null
          id: number
          modificato_da: string | null
          modificato_il: string | null
          nome: string | null
          numero_telefono: string | null
          residenza: string | null
        }
        Insert: {
          biografia?: string | null
          categoria?: string | null
          categoria_artisti_id?: number | null
          creato_da?: string | null
          creato_il?: string | null
          data_nascita?: string | null
          email?: string | null
          id?: number
          modificato_da?: string | null
          modificato_il?: string | null
          nome?: string | null
          numero_telefono?: string | null
          residenza?: string | null
        }
        Update: {
          biografia?: string | null
          categoria?: string | null
          categoria_artisti_id?: number | null
          creato_da?: string | null
          creato_il?: string | null
          data_nascita?: string | null
          email?: string | null
          id?: number
          modificato_da?: string | null
          modificato_il?: string | null
          nome?: string | null
          numero_telefono?: string | null
          residenza?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "artisti_categoria_artisti_id_fkey"
            columns: ["categoria_artisti_id"]
            isOneToOne: false
            referencedRelation: "categorie_artisti"
            referencedColumns: ["id"]
          },
        ]
      }
      categorie: {
        Row: {
          creato_da: string | null
          creato_il: string | null
          descrizione: string | null
          icona: string | null
          id: number
          in_evidenza_id: number | null
          modificato_da: string | null
          modificato_il: string | null
          nome: string | null
          per_il_turista: boolean | null
          priorità_tipo: number | null
          sequenza_visualizzazione: number | null
          tipo: string | null
        }
        Insert: {
          creato_da?: string | null
          creato_il?: string | null
          descrizione?: string | null
          icona?: string | null
          id?: number
          in_evidenza_id?: number | null
          modificato_da?: string | null
          modificato_il?: string | null
          nome?: string | null
          per_il_turista?: boolean | null
          priorità_tipo?: number | null
          sequenza_visualizzazione?: number | null
          tipo?: string | null
        }
        Update: {
          creato_da?: string | null
          creato_il?: string | null
          descrizione?: string | null
          icona?: string | null
          id?: number
          in_evidenza_id?: number | null
          modificato_da?: string | null
          modificato_il?: string | null
          nome?: string | null
          per_il_turista?: boolean | null
          priorità_tipo?: number | null
          sequenza_visualizzazione?: number | null
          tipo?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "categorie_in_evidenza_id_fkey"
            columns: ["in_evidenza_id"]
            isOneToOne: false
            referencedRelation: "in_evidenza"
            referencedColumns: ["id"]
          },
        ]
      }
      categorie_artisti: {
        Row: {
          creato_da: string | null
          creato_il: string | null
          id: number
          modificato_da: string | null
          modificato_il: string | null
          nome: string | null
        }
        Insert: {
          creato_da?: string | null
          creato_il?: string | null
          id?: number
          modificato_da?: string | null
          modificato_il?: string | null
          nome?: string | null
        }
        Update: {
          creato_da?: string | null
          creato_il?: string | null
          id?: number
          modificato_da?: string | null
          modificato_il?: string | null
          nome?: string | null
        }
        Relationships: []
      }
      comuni: {
        Row: {
          categorie_da_escludere: string | null
          creato_da: string | null
          creato_il: string | null
          email: string | null
          id: number
          indirizzo: string | null
          latitude: number | null
          link_albo_pretorio: string | null
          link_prenotazioni: string | null
          longitude: number | null
          modificato_da: string | null
          modificato_il: string | null
          nome: string | null
          pec: string | null
          provincia: string | null
          sequenza_visualizzazione: number | null
          telefono: string | null
          telefono_vigili: string | null
          visibile: boolean | null
        }
        Insert: {
          categorie_da_escludere?: string | null
          creato_da?: string | null
          creato_il?: string | null
          email?: string | null
          id?: number
          indirizzo?: string | null
          latitude?: number | null
          link_albo_pretorio?: string | null
          link_prenotazioni?: string | null
          longitude?: number | null
          modificato_da?: string | null
          modificato_il?: string | null
          nome?: string | null
          pec?: string | null
          provincia?: string | null
          sequenza_visualizzazione?: number | null
          telefono?: string | null
          telefono_vigili?: string | null
          visibile?: boolean | null
        }
        Update: {
          categorie_da_escludere?: string | null
          creato_da?: string | null
          creato_il?: string | null
          email?: string | null
          id?: number
          indirizzo?: string | null
          latitude?: number | null
          link_albo_pretorio?: string | null
          link_prenotazioni?: string | null
          longitude?: number | null
          modificato_da?: string | null
          modificato_il?: string | null
          nome?: string | null
          pec?: string | null
          provincia?: string | null
          sequenza_visualizzazione?: number | null
          telefono?: string | null
          telefono_vigili?: string | null
          visibile?: boolean | null
        }
        Relationships: []
      }
      configurazioni: {
        Row: {
          id: number
          nome: string | null
          valore: string | null
        }
        Insert: {
          id?: number
          nome?: string | null
          valore?: string | null
        }
        Update: {
          id?: number
          nome?: string | null
          valore?: string | null
        }
        Relationships: []
      }
      eventi: {
        Row: {
          comune_id: number | null
          creato_da: string | null
          creato_il: string | null
          data: string | null
          descrizione: string | null
          id: number
          in_evidenza_id: number | null
          luogo: string | null
          modificato_da: string | null
          modificato_il: string | null
          ora_fine: string | null
          ora_inizio: string | null
          premium: boolean | null
          titolo: string | null
        }
        Insert: {
          comune_id?: number | null
          creato_da?: string | null
          creato_il?: string | null
          data?: string | null
          descrizione?: string | null
          id?: number
          in_evidenza_id?: number | null
          luogo?: string | null
          modificato_da?: string | null
          modificato_il?: string | null
          ora_fine?: string | null
          ora_inizio?: string | null
          premium?: boolean | null
          titolo?: string | null
        }
        Update: {
          comune_id?: number | null
          creato_da?: string | null
          creato_il?: string | null
          data?: string | null
          descrizione?: string | null
          id?: number
          in_evidenza_id?: number | null
          luogo?: string | null
          modificato_da?: string | null
          modificato_il?: string | null
          ora_fine?: string | null
          ora_inizio?: string | null
          premium?: boolean | null
          titolo?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "eventi_comune_id_fkey"
            columns: ["comune_id"]
            isOneToOne: false
            referencedRelation: "comuni"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "eventi_in_evidenza_id_fkey"
            columns: ["in_evidenza_id"]
            isOneToOne: false
            referencedRelation: "in_evidenza"
            referencedColumns: ["id"]
          },
        ]
      }
      giunte_comunali: {
        Row: {
          biografia: string | null
          cognome: string | null
          comune_id: number | null
          contatti: string | null
          creato_da: string | null
          creato_il: string | null
          data_nascita: string | null
          deleghe: string | null
          id: number
          incarico: string | null
          modificato_da: string | null
          modificato_il: string | null
          nome: string | null
          sequenza_visualizzazione: number | null
        }
        Insert: {
          biografia?: string | null
          cognome?: string | null
          comune_id?: number | null
          contatti?: string | null
          creato_da?: string | null
          creato_il?: string | null
          data_nascita?: string | null
          deleghe?: string | null
          id?: number
          incarico?: string | null
          modificato_da?: string | null
          modificato_il?: string | null
          nome?: string | null
          sequenza_visualizzazione?: number | null
        }
        Update: {
          biografia?: string | null
          cognome?: string | null
          comune_id?: number | null
          contatti?: string | null
          creato_da?: string | null
          creato_il?: string | null
          data_nascita?: string | null
          deleghe?: string | null
          id?: number
          incarico?: string | null
          modificato_da?: string | null
          modificato_il?: string | null
          nome?: string | null
          sequenza_visualizzazione?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "giunte_comunali_comune_id_fkey"
            columns: ["comune_id"]
            isOneToOne: false
            referencedRelation: "comuni"
            referencedColumns: ["id"]
          },
        ]
      }
      in_evidenza: {
        Row: {
          creato_da: string | null
          creato_il: string | null
          descrizione: string | null
          id: number
          modificato_da: string | null
          modificato_il: string | null
          sequenza_visualizzazione: number | null
          titolo: string | null
        }
        Insert: {
          creato_da?: string | null
          creato_il?: string | null
          descrizione?: string | null
          id?: number
          modificato_da?: string | null
          modificato_il?: string | null
          sequenza_visualizzazione?: number | null
          titolo?: string | null
        }
        Update: {
          creato_da?: string | null
          creato_il?: string | null
          descrizione?: string | null
          id?: number
          modificato_da?: string | null
          modificato_il?: string | null
          sequenza_visualizzazione?: number | null
          titolo?: string | null
        }
        Relationships: []
      }
      indirizzi: {
        Row: {
          cap: string | null
          città: string | null
          comune: string | null
          creato_da: string | null
          creato_il: string | null
          id: number
          latitude: number | null
          longitude: number | null
          modificato_da: string | null
          modificato_il: string | null
          nazione: string | null
          numero_civico: string | null
          provincia: string | null
          servizio_id: number | null
          via: string | null
        }
        Insert: {
          cap?: string | null
          città?: string | null
          comune?: string | null
          creato_da?: string | null
          creato_il?: string | null
          id?: number
          latitude?: number | null
          longitude?: number | null
          modificato_da?: string | null
          modificato_il?: string | null
          nazione?: string | null
          numero_civico?: string | null
          provincia?: string | null
          servizio_id?: number | null
          via?: string | null
        }
        Update: {
          cap?: string | null
          città?: string | null
          comune?: string | null
          creato_da?: string | null
          creato_il?: string | null
          id?: number
          latitude?: number | null
          longitude?: number | null
          modificato_da?: string | null
          modificato_il?: string | null
          nazione?: string | null
          numero_civico?: string | null
          provincia?: string | null
          servizio_id?: number | null
          via?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "indirizzi_servizio_id_fkey"
            columns: ["servizio_id"]
            isOneToOne: false
            referencedRelation: "servizi"
            referencedColumns: ["id"]
          },
        ]
      }
      orari: {
        Row: {
          creato_da: string | null
          creato_il: string | null
          domenica: string | null
          giovedì: string | null
          h24: boolean | null
          id: number
          lunedì: string | null
          martedì: string | null
          mercoledì: string | null
          modificato_da: string | null
          modificato_il: string | null
          sabato: string | null
          servizio_id: number | null
          venerdì: string | null
        }
        Insert: {
          creato_da?: string | null
          creato_il?: string | null
          domenica?: string | null
          giovedì?: string | null
          h24?: boolean | null
          id?: number
          lunedì?: string | null
          martedì?: string | null
          mercoledì?: string | null
          modificato_da?: string | null
          modificato_il?: string | null
          sabato?: string | null
          servizio_id?: number | null
          venerdì?: string | null
        }
        Update: {
          creato_da?: string | null
          creato_il?: string | null
          domenica?: string | null
          giovedì?: string | null
          h24?: boolean | null
          id?: number
          lunedì?: string | null
          martedì?: string | null
          mercoledì?: string | null
          modificato_da?: string | null
          modificato_il?: string | null
          sabato?: string | null
          servizio_id?: number | null
          venerdì?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "orari_servizio_id_fkey"
            columns: ["servizio_id"]
            isOneToOne: false
            referencedRelation: "servizi"
            referencedColumns: ["id"]
          },
        ]
      }
      richieste_contatto: {
        Row: {
          cognome: string
          created_at: string
          creato_da: string | null
          creato_il: string | null
          data_richiesta: string
          email: string
          id: string
          messaggio: string
          modificato_da: string | null
          modificato_il: string | null
          nome: string
          stato: string | null
        }
        Insert: {
          cognome: string
          created_at?: string
          creato_da?: string | null
          creato_il?: string | null
          data_richiesta?: string
          email: string
          id?: string
          messaggio: string
          modificato_da?: string | null
          modificato_il?: string | null
          nome: string
          stato?: string | null
        }
        Update: {
          cognome?: string
          created_at?: string
          creato_da?: string | null
          creato_il?: string | null
          data_richiesta?: string
          email?: string
          id?: string
          messaggio?: string
          modificato_da?: string | null
          modificato_il?: string | null
          nome?: string
          stato?: string | null
        }
        Relationships: []
      }
      richieste_inserimento_attivita: {
        Row: {
          cap: string
          categoria: string
          codice_intermediario: string | null
          created_at: string
          creato_da: string | null
          creato_il: string | null
          data_richiesta: string
          email: string
          email_visibilita: string
          id: string
          indirizzo_visibilita: string
          modificato_da: string | null
          modificato_il: string | null
          nome_azienda: string
          note: string | null
          paese: string
          paese_visibilita: string
          piva_cf: string
          ragione_sociale: string
          responsabile_cognome: string
          responsabile_nome: string
          sito_web: string | null
          stato: string | null
          telefono: string
          telefono_visibilita: string
          via: string
          video_link: string | null
        }
        Insert: {
          cap: string
          categoria: string
          codice_intermediario?: string | null
          created_at?: string
          creato_da?: string | null
          creato_il?: string | null
          data_richiesta?: string
          email: string
          email_visibilita: string
          id?: string
          indirizzo_visibilita: string
          modificato_da?: string | null
          modificato_il?: string | null
          nome_azienda: string
          note?: string | null
          paese: string
          paese_visibilita: string
          piva_cf: string
          ragione_sociale: string
          responsabile_cognome: string
          responsabile_nome: string
          sito_web?: string | null
          stato?: string | null
          telefono: string
          telefono_visibilita: string
          via: string
          video_link?: string | null
        }
        Update: {
          cap?: string
          categoria?: string
          codice_intermediario?: string | null
          created_at?: string
          creato_da?: string | null
          creato_il?: string | null
          data_richiesta?: string
          email?: string
          email_visibilita?: string
          id?: string
          indirizzo_visibilita?: string
          modificato_da?: string | null
          modificato_il?: string | null
          nome_azienda?: string
          note?: string | null
          paese?: string
          paese_visibilita?: string
          piva_cf?: string
          ragione_sociale?: string
          responsabile_cognome?: string
          responsabile_nome?: string
          sito_web?: string | null
          stato?: string | null
          telefono?: string
          telefono_visibilita?: string
          via?: string
          video_link?: string | null
        }
        Relationships: []
      }
      servizi: {
        Row: {
          categoria_id: number | null
          comuni_aggiuntivi: string | null
          creato_da: string | null
          creato_il: string | null
          descrizione: string | null
          dintorni_di: string | null
          email: string | null
          id: number
          in_evidenza_id: number | null
          link_diretta_streaming: string | null
          link_prenotazioni: string | null
          modificato_da: string | null
          modificato_il: string | null
          nome: string | null
          per_il_turista: boolean | null
          premium: boolean | null
          sito_web: string | null
          telefono_primario: string | null
          telefono_secondario: string | null
          tipologia: string | null
        }
        Insert: {
          categoria_id?: number | null
          comuni_aggiuntivi?: string | null
          creato_da?: string | null
          creato_il?: string | null
          descrizione?: string | null
          dintorni_di?: string | null
          email?: string | null
          id?: number
          in_evidenza_id?: number | null
          link_diretta_streaming?: string | null
          link_prenotazioni?: string | null
          modificato_da?: string | null
          modificato_il?: string | null
          nome?: string | null
          per_il_turista?: boolean | null
          premium?: boolean | null
          sito_web?: string | null
          telefono_primario?: string | null
          telefono_secondario?: string | null
          tipologia?: string | null
        }
        Update: {
          categoria_id?: number | null
          comuni_aggiuntivi?: string | null
          creato_da?: string | null
          creato_il?: string | null
          descrizione?: string | null
          dintorni_di?: string | null
          email?: string | null
          id?: number
          in_evidenza_id?: number | null
          link_diretta_streaming?: string | null
          link_prenotazioni?: string | null
          modificato_da?: string | null
          modificato_il?: string | null
          nome?: string | null
          per_il_turista?: boolean | null
          premium?: boolean | null
          sito_web?: string | null
          telefono_primario?: string | null
          telefono_secondario?: string | null
          tipologia?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "servizi_categoria_id_fkey"
            columns: ["categoria_id"]
            isOneToOne: false
            referencedRelation: "categorie"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "servizi_in_evidenza_id_fkey"
            columns: ["in_evidenza_id"]
            isOneToOne: false
            referencedRelation: "in_evidenza"
            referencedColumns: ["id"]
          },
        ]
      }
      video: {
        Row: {
          comune_id: number | null
          creato_da: string | null
          creato_il: string | null
          descrizione: string | null
          id: number
          in_evidenza_id: number | null
          link: string | null
          modificato_da: string | null
          modificato_il: string | null
          servizio_id: number | null
          titolo: string | null
        }
        Insert: {
          comune_id?: number | null
          creato_da?: string | null
          creato_il?: string | null
          descrizione?: string | null
          id?: number
          in_evidenza_id?: number | null
          link?: string | null
          modificato_da?: string | null
          modificato_il?: string | null
          servizio_id?: number | null
          titolo?: string | null
        }
        Update: {
          comune_id?: number | null
          creato_da?: string | null
          creato_il?: string | null
          descrizione?: string | null
          id?: number
          in_evidenza_id?: number | null
          link?: string | null
          modificato_da?: string | null
          modificato_il?: string | null
          servizio_id?: number | null
          titolo?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "video_comune_id_fkey"
            columns: ["comune_id"]
            isOneToOne: false
            referencedRelation: "comuni"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_in_evidenza_id_fkey"
            columns: ["in_evidenza_id"]
            isOneToOne: false
            referencedRelation: "in_evidenza"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_servizio_id_fkey"
            columns: ["servizio_id"]
            isOneToOne: false
            referencedRelation: "servizi"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {},
  },
} as const
