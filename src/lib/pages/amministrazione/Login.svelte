<script lang="ts">
  import { <PERSON><PERSON> } from "$lib/components/ui/button";
  import { Card, CardContent, CardHeader, CardTitle } from "$lib/components/ui/card";
  import { Input } from "$lib/components/ui/input";
  import { cn } from "$lib/utils";
  import Navbar from "$lib/components/Navbar.svelte";
  import Footer from "$lib/components/Footer.svelte";
  import { authService } from "$lib/supabase/auth";
  import { push } from "svelte-spa-router";
  import { toast } from "svelte-sonner";
  const props = $props<{ className?: string }>();
  const className = props.className ?? "";
  
  let email = $state("");
  let password = $state("");
  let loading = $state(false);
  
  async function handleLogin() {
    try {
      loading = true;
      const { user, session } = await authService.signIn(email, password);
      if(user && session) {
        push("/amministrazione");
      } else {
        toast.error("Si è verificato un errore durante l'accesso");
      }
    } catch (e: any) {
      toast.error("Si è verificato un errore durante l'accesso");
    } finally {
      loading = false;
    }
  }
</script>

<div class={cn("min-h-screen bg-background font-sans", className)}>
  <Navbar />
  
  <main class="container flex items-center justify-center min-h-[calc(100vh-3.5rem)] py-8">
    <Card class="w-full max-w-lg border-none shadow-lg">
      <CardHeader class="space-y-3">
        <CardTitle class="text-3xl font-medium tracking-tight text-center">
          Accedi
        </CardTitle>
        <p class="text-muted-foreground text-lg text-center font-light">
          Inserisci le tue credenziali per accedere
        </p>
      </CardHeader>
      
      <CardContent class="space-y-6">
        <form onsubmit={e => { e.preventDefault(); handleLogin(); }} class="space-y-4">
          <div class="space-y-2">
            <label for="email" class="text-sm font-medium">Email</label>
            <Input
              id="email"
              type="email"
              bind:value={email}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div class="space-y-2">
            <label for="password" class="text-sm font-medium">Password</label>
            <Input
              id="password"
              type="password"
              bind:value={password}
              placeholder="••••••••"
              required
            />
          </div>

          <div class="flex justify-between items-center text-sm">
            <a 
              href="/#/amministrazione/reset-password" 
              class="text-primary hover:underline"
            >
              Password dimenticata?
            </a>
          </div>

          <Button 
            type="submit" 
            class="w-full"
            disabled={loading}
          >
            {loading ? "Accesso in corso..." : "Accedi"}
          </Button>
        </form>
      </CardContent>
    </Card>
  </main>

  <Footer />
</div>
