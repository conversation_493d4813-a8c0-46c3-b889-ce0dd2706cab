<script lang="ts">
  import AdminLayout from "$lib/components/ui/layout/AdminLayout.svelte";
  import DataTable from "$lib/components/ui/data-table/DataTable.svelte";
  import EntityDialog from "$lib/components/ui/dialog/EntityDialog.svelte";
  import { Button } from "$lib/components/ui/button";
  import { CrudService } from "$lib/supabase/crud";
  import { toast } from "svelte-sonner";
  import { Edit, Trash2, LayoutList, ArrowLeft, Upload } from "lucide-svelte";
  import type { Tables } from "$lib/supabase/database.types";
  import { push } from "svelte-spa-router";
  import { Select, SelectTrigger, SelectContent, SelectItem } from "$lib/components/ui/select";
  import { StorageService } from '$lib/supabase/storage';
  import { Avatar, AvatarImage, AvatarFallback } from "$lib/components/ui/avatar/index.js";
  import { Dialog, DialogContent, DialogHeader, DialogTitle } from "$lib/components/ui/dialog";

  type Categoria = Tables<'categorie'>;

  const categorieService = new CrudService('categorie');
  const storageService = new StorageService('categorie');

  let categorie = $state<Categoria[]>([]);
  let loading = $state(true);
  let dialogOpen = $state(false);
  let editingCategoria = $state<Categoria | null>(null);
  let saving = $state(false);
  let searchQuery = $state('');
  let sortField = $state<keyof Categoria | null>(null);
  let sortDirection = $state<'asc' | 'desc'>('asc');
  let filteredCategorie = $derived(getFilteredCategorie());
  let imageUploading = $state(false);
  let imagePreviewOpen = $state(false);
  let previewingImage = $state<string | null>(null);
  let imageTimestamps = $state<Record<string, number>>({});

  async function loadCategorie() {
    try {
      loading = true;
      categorie = await categorieService.getAll();
    } catch (error) {
      toast.error("Errore nel caricamento delle categorie");
    } finally {
      loading = false;
    }
  }

  $effect(() => {
    loadCategorie();
  });

  function handleAdd() {
    editingCategoria = null;
    dialogOpen = true;
  }

  function handleEdit(categoria: Categoria) {
    editingCategoria = categoria;
    dialogOpen = true;
  }

  async function handleDelete(id: number) {
    try {
      await categorieService.delete(id);
      toast.success("Categoria eliminata con successo");
      await loadCategorie();
    } catch (error) {
      toast.error("Errore nell'eliminazione della categoria");
    }
  }

  async function handleSubmit(data: Record<string, any>) {
    try {
      saving = true;
      if (editingCategoria) {
        await categorieService.update(editingCategoria.id, data);
        toast.success("Categoria aggiornata con successo");
      } else {
        await categorieService.create(data);
        toast.success("Categoria creata con successo");
      }
      dialogOpen = false;
      await loadCategorie();
    } catch (error) {
      toast.error("Errore nel salvataggio della categoria");
    } finally {
      saving = false;
    }
  }

  function handleSearch(event: Event) {
    const target = event.target as HTMLInputElement;
    searchQuery = target.value;
  }

  function handleServizi(categoria: Categoria) {
    push(`/amministrazione/categorie/${categoria.id}/servizi`);
  }

  function handleBack() {
    push('/amministrazione');
  }

  function handleSort(field: keyof Categoria) {
    if (sortField === field) {
      sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      sortField = field;
      sortDirection = 'asc';
    }
  }

  function getSortIcon(field: keyof Categoria) {
    if (sortField !== field) return '↕';
    return sortDirection === 'asc' ? '↑' : '↓';
  }

  function getSortOptions() {
    return [
      { value: 'nome', label: 'Nome' },
      { value: 'tipo', label: 'Tipo' },
      { value: 'sequenza_visualizzazione', label: 'Sequenza' }
    ];
  }

  function getFilteredCategorie(): Categoria[] {
    let filtered = !searchQuery.trim()
      ? categorie
      : categorie.filter(categoria => {
          const query = searchQuery.toLowerCase();
          return (
            (categoria.nome?.toLowerCase() || '').includes(query) ||
            (categoria.tipo?.toLowerCase() || '').includes(query)
          );
        });

    if (sortField) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = sortField ? a[sortField] : null;
        const bValue = sortField ? b[sortField] : null;

        if (aValue === null || aValue === undefined) return sortDirection === 'asc' ? 1 : -1;
        if (bValue === null || bValue === undefined) return sortDirection === 'asc' ? -1 : 1;

        const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }

    return filtered;
  }

  async function handleImageUpload(event: Event, categoria: Categoria) {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    if (!file || !categoria.nome) return;

    try {
        imageUploading = true;
        const fileName = `${categoria.nome}.jpg`;
        await storageService.uploadFile(file, fileName);

        imageTimestamps[categoria.nome] = Date.now();

        await loadCategorie();
        toast.success("Immagine caricata con successo");
    } catch (error) {
        console.error('Upload error:', error);
        toast.error("Errore nel caricamento dell'immagine");
    } finally {
        imageUploading = false;
        input.value = '';
    }
  }

  function getCategoryImage(categoria: Categoria) {
    if (!categoria?.nome) return '';
    const timestamp = imageTimestamps[categoria.nome] || '';
    return storageService.getImageUrl(`${categoria.nome}.jpg`) + (timestamp ? `?t=${timestamp}` : '');
  }

  function handleImageClick(imageUrl: string) {
    previewingImage = imageUrl;
    imagePreviewOpen = true;
  }
</script>

<AdminLayout>
  <div class="mb-4">
    <Button variant="ghost" onclick={handleBack}>
      <ArrowLeft class="mr-2 h-4 w-4" />
      Torna alla dashboard
    </Button>
  </div>

  <DataTable
    title="Categorie"
    description="Gestisci le categorie dei servizi"
    onAdd={handleAdd}
    searchPlaceholder="Cerca categorie..."
    onSearch={handleSearch}
  >
    {#snippet actions()}
      <Select
        type="single"
        value={sortField || ''}
        onValueChange={(value: string) => handleSort(value as keyof Categoria)}
      >
        <SelectTrigger class="w-[150px] bg-transparent border-none shadow-none hover:bg-accent hover:text-accent-foreground focus:ring-0">
          {sortField
            ? `${getSortOptions().find(opt => opt.value === sortField)?.label} ${sortDirection === 'asc' ? '↑' : '↓'}`
            : 'Ordina per...'
          }
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="">Nessun ordinamento</SelectItem>
          {#each getSortOptions() as option}
            <SelectItem value={option.value}>
              {option.label}
            </SelectItem>
          {/each}
        </SelectContent>
      </Select>
    {/snippet}

    <div class="relative overflow-x-auto">
      <!-- Mobile view (card layout) -->
      <div class="md:hidden space-y-4">
        {#if loading}
          <div class="p-4 text-center">Caricamento...</div>
        {:else if categorie.length === 0}
          <div class="p-4 text-center">Nessuna categoria trovata</div>
        {:else}
          {#each filteredCategorie as categoria}
            <div class="bg-card rounded-lg shadow p-4 space-y-2">
              <div class="flex justify-between items-start">
                <div class="flex items-center gap-4">
                  <div class="relative">
                    <Avatar
                      class="h-12 w-12 cursor-pointer"
                      onclick={() => handleImageClick(getCategoryImage(categoria))}
                    >
                      <AvatarImage src={getCategoryImage(categoria)} alt={categoria.nome} />
                      <AvatarFallback>{categoria.nome?.[0]?.toUpperCase()}</AvatarFallback>
                    </Avatar>
                  </div>
                  <div>
                    <h3 class="font-medium">{categoria.nome}</h3>
                    <p class="text-sm text-muted-foreground">{categoria.tipo}</p>
                  </div>
                </div>
                <div class="flex space-x-2">
                  <Button variant="ghost" size="icon" onclick={() => handleServizi(categoria)}>
                    <LayoutList size={16} />
                  </Button>
                  <Button variant="ghost" size="icon" onclick={() => handleEdit(categoria)}>
                    <Edit size={16} />
                  </Button>
                  <Button variant="ghost" size="icon" onclick={() => handleDelete(categoria.id)}>
                    <Trash2 size={16} />
                  </Button>
                </div>
              </div>
              <div class="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span class="text-muted-foreground">Icona:</span> {categoria.icona}
                </div>
                <div>
                  <span class="text-muted-foreground">Priorità:</span> {categoria.priorità_tipo}
                </div>
                <div>
                  <span class="text-muted-foreground">Sequenza:</span> {categoria.sequenza_visualizzazione}
                </div>
              </div>
            </div>
          {/each}
        {/if}
      </div>

      <!-- Desktop view (table layout) -->
      <table class="w-full text-sm text-left hidden md:table">
        <thead class="text-sm bg-muted">
          <tr>
            <th class="px-6 py-3">Immagine</th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('nome')}
              >
                <span>Nome</span>
                <span class="text-muted-foreground">{getSortIcon('nome')}</span>
              </button>
            </th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('tipo')}
              >
                <span>Tipo</span>
                <span class="text-muted-foreground">{getSortIcon('tipo')}</span>
              </button>
            </th>
            <th class="px-6 py-3">Icona</th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('priorità_tipo')}
              >
                <span>Priorità</span>
                <span class="text-muted-foreground">{getSortIcon('priorità_tipo')}</span>
              </button>
            </th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('sequenza_visualizzazione')}
              >
                <span>Sequenza</span>
                <span class="text-muted-foreground">{getSortIcon('sequenza_visualizzazione')}</span>
              </button>
            </th>
            <th class="px-6 py-3 text-right">Azioni</th>
          </tr>
        </thead>
        <tbody>
          {#if loading}
            <tr>
              <td colspan="6" class="px-6 py-4 text-center">Caricamento...</td>
            </tr>
          {:else if categorie.length === 0}
            <tr>
              <td colspan="6" class="px-6 py-4 text-center">Nessuna categoria trovata</td>
            </tr>
          {:else}
            {#each filteredCategorie as categoria}
              <tr class="border-b hover:bg-muted/50">
                <td class="px-6 py-4">
                  <div class="relative inline-block">
                    <Avatar
                      class="h-12 w-12 cursor-pointer"
                      onclick={() => handleImageClick(getCategoryImage(categoria))}
                    >
                      <AvatarImage src={getCategoryImage(categoria)} alt={categoria.nome} />
                      <AvatarFallback>{categoria.nome?.[0]?.toUpperCase()}</AvatarFallback>
                    </Avatar>
                  </div>
                </td>
                <td class="px-6 py-4">{categoria.nome}</td>
                <td class="px-6 py-4">{categoria.tipo}</td>
                <td class="px-6 py-4">{categoria.icona}</td>
                <td class="px-6 py-4">{categoria.priorità_tipo}</td>
                <td class="px-6 py-4">{categoria.sequenza_visualizzazione}</td>
                <td class="px-6 py-4 text-right space-x-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleServizi(categoria)}
                  >
                    <LayoutList size={16} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleEdit(categoria)}
                  >
                    <Edit size={16} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleDelete(categoria.id)}
                  >
                    <Trash2 size={16} />
                  </Button>
                </td>
              </tr>
            {/each}
          {/if}
        </tbody>
      </table>
    </div>
  </DataTable>

  <EntityDialog
    open={dialogOpen}
    title={editingCategoria ? "Modifica categoria" : "Nuova categoria"}
    description={editingCategoria ? "Modifica i dati della categoria" : "Inserisci i dati della nuova categoria"}
    fields={[
      { key: "nome", label: "Nome", required: true, value: editingCategoria?.nome ?? "" },
      { key: "tipo", label: "Tipo", required: true, value: editingCategoria?.tipo ?? "" },
      { key: "icona", label: "Icona", value: editingCategoria?.icona ?? "" },
      { key: "priorità_tipo", label: "Priorità", type: "number", value: editingCategoria?.priorità_tipo ?? 0 },
      { key: "sequenza_visualizzazione", label: "Sequenza", type: "number", value: editingCategoria?.sequenza_visualizzazione ?? 0 },
      { key: "per_il_turista", label: "Per il turista", type: "checkbox", value: editingCategoria?.per_il_turista ?? false },
      { key: "in_evidenza_id", label: "In evidenza", type: "number", value: editingCategoria?.in_evidenza_id ?? null }
    ]}
    onSubmit={handleSubmit}
    onCancel={() => dialogOpen = false}
    loading={saving}
  />
</AdminLayout>

<Dialog open={imagePreviewOpen} onOpenChange={(open) => imagePreviewOpen = open}>
  <DialogContent class="max-w-3xl">
    <DialogHeader>
      <DialogTitle>Anteprima immagine</DialogTitle>
    </DialogHeader>
    <div class="flex flex-col gap-4">
      <img src={previewingImage} alt="Preview" class="w-full rounded-lg" />
      <div class="flex justify-center">
        <div>
          <input
            type="file"
            accept="image/jpeg"
            class="hidden"
            id="imageUpload"
            onchange={(e) => {
              const categoria = categorie.find(c => getCategoryImage(c) === previewingImage);
              if (categoria) {
                handleImageUpload(e, categoria);
                imagePreviewOpen = false;
              }
            }}
            disabled={imageUploading}
          />
          <Button
            variant="outline"
            class="gap-2"
            type="button"
            onclick={() => document.getElementById('imageUpload')?.click()}
          >
            <Upload class="h-4 w-4" />
            Sostituisci
          </Button>
        </div>
      </div>
    </div>
  </DialogContent>
</Dialog>