<script lang="ts">
  import AdminLayout from "$lib/components/ui/layout/AdminLayout.svelte";
  import DataTable from "$lib/components/ui/data-table/DataTable.svelte";
  import EntityDialog from "$lib/components/ui/dialog/EntityDialog.svelte";
  import { Button } from "$lib/components/ui/button";
  import { CrudService } from "$lib/supabase/crud";
  import { toast } from "svelte-sonner";
  import { Edit, Trash2, Users, ArrowLeft, Upload } from "lucide-svelte";
  import type { Tables } from "$lib/supabase/database.types";
  import { push } from "svelte-spa-router";
  import { Select, SelectContent, SelectItem, SelectTrigger } from "$lib/components/ui/select";
  import { StorageService } from '$lib/supabase/storage';
  import { Avatar, AvatarImage, AvatarFallback } from "$lib/components/ui/avatar/index.js";
  import { Dialog, DialogContent, DialogHeader, DialogTitle } from "$lib/components/ui/dialog";

  type Comune = Tables<'comuni'>;

  const comuniService = new CrudService('comuni');
  const storageService = new StorageService('comuni');

  let comuni = $state<Comune[]>([]);
  let searchQuery = $state('');
  let sortField = $state<keyof Comune | null>(null);
  let sortDirection = $state<'asc' | 'desc'>('asc');
  let filteredComuni = $derived(getFilteredComuni());
  let loading = $state(true);
  let dialogOpen = $state(false);
  let editingComune = $state<Comune | null>(null);
  let saving = $state(false);
  let imageTimestamps = $state<Record<string, number>>({});
  let imageUploading = $state(false);
  let imagePreviewOpen = $state(false);
  let previewingImage = $state('');

  async function loadComuni() {
    try {
      loading = true;
      comuni = await comuniService.getAll();
    } catch (error) {
      toast.error("Errore nel caricamento dei comuni");
    } finally {
      loading = false;
    }
  }

  $effect(() => {
    loadComuni();
  });

  function handleAdd() {
    editingComune = null;
    dialogOpen = true;
  }

  function handleEdit(comune: Comune) {
    editingComune = comune;
    dialogOpen = true;
  }

  async function handleDelete(id: number) {
    try {
      await comuniService.delete(id);
      toast.success("Comune eliminato con successo");
      await loadComuni();
    } catch (error) {
      toast.error("Errore nell'eliminazione del comune");
    }
  }

  async function handleSubmit(data: Record<string, any>) {
    try {
      saving = true;
      if (editingComune) {
        await comuniService.update(editingComune.id, data);
        toast.success("Comune aggiornato con successo");
      } else {
        await comuniService.create(data);
        toast.success("Comune creato con successo");
      }
      dialogOpen = false;
      await loadComuni();
    } catch (error) {
      toast.error("Errore nel salvataggio del comune");
    } finally {
      saving = false;
    }
  }

  function handleGiunta(comune: Comune) {
    push(`/amministrazione/comuni/${comune.id}/giunta`);
  }

  function handleSearch(event: Event) {
    const target = event.target as HTMLInputElement;
    searchQuery = target.value;
  }

  function handleBack() {
    push('/amministrazione');
  }

  function handleSort(field: keyof Comune) {
    if (sortField === field) {
      sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      sortField = field;
      sortDirection = 'asc';
    }
  }

  function getSortIcon(field: keyof Comune) {
    if (sortField !== field) return '↕';
    return sortDirection === 'asc' ? '↑' : '↓';
  }

  function getFilteredComuni(): Comune[] {
    let filtered = !searchQuery.trim()
      ? comuni
      : comuni.filter(comune => {
          const query = searchQuery.toLowerCase();
          return (
            (comune.nome?.toLowerCase() || '').includes(query) ||
            (comune.provincia?.toLowerCase() || '').includes(query) ||
            (comune.telefono?.toLowerCase() || '').includes(query)
          );
        });

    if (sortField) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = sortField ? a[sortField] : null;
        const bValue = sortField ? b[sortField] : null;

        if (aValue === null || aValue === undefined) return sortDirection === 'asc' ? 1 : -1;
        if (bValue === null || bValue === undefined) return sortDirection === 'asc' ? -1 : 1;

        const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }

    return filtered;
  }

  function getSortOptions() {
    return [
      { value: 'nome', label: 'Nome' },
      { value: 'provincia', label: 'Provincia' },
      { value: 'sequenza_visualizzazione', label: 'Sequenza' }
    ];
  }

  async function handleImageUpload(event: Event, comune: Comune) {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    if (!file || !comune.nome) return;

    try {
        imageUploading = true;
        const fileName = `${comune.nome}.jpg`;
        await storageService.uploadFile(file, fileName);

        imageTimestamps[comune.nome] = Date.now();

        await loadComuni();
        toast.success("Immagine caricata con successo");
    } catch (error) {
        console.error('Upload error:', error);
        toast.error("Errore nel caricamento dell'immagine");
    } finally {
        imageUploading = false;
        input.value = '';
    }
  }

  function getComuneImage(comune: Comune) {
    if (!comune?.nome) return '';
    const timestamp = imageTimestamps[comune.nome] || '';
    return storageService.getImageUrl(`${comune.nome}.jpg`) + (timestamp ? `?t=${timestamp}` : '');
  }

  function handleImageClick(imageUrl: string) {
    previewingImage = imageUrl;
    imagePreviewOpen = true;
  }
</script>

<AdminLayout>
  <div class="mb-4">
    <Button variant="ghost" onclick={handleBack}>
      <ArrowLeft class="mr-2 h-4 w-4" />
      Torna alla dashboard
    </Button>
  </div>

  <DataTable
    title="Comuni"
    description="Gestisci i comuni e le relative giunte comunali"
    onAdd={handleAdd}
    searchPlaceholder="Cerca comuni..."
    onSearch={handleSearch}
  >
    {#snippet actions()}
      <Select
        type="single"
        value={sortField || ''}
        onValueChange={(value: string) => handleSort(value as keyof Comune)}
      >
        <SelectTrigger class="w-[150px] bg-transparent border-none shadow-none hover:bg-accent hover:text-accent-foreground focus:ring-0">
          {sortField
            ? `${getSortOptions().find(opt => opt.value === sortField)?.label} ${sortDirection === 'asc' ? '↑' : '↓'}`
            : 'Ordina per...'
          }
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="">Nessun ordinamento</SelectItem>
          {#each getSortOptions() as option}
            <SelectItem value={option.value}>
              {option.label}
            </SelectItem>
          {/each}
        </SelectContent>
      </Select>
    {/snippet}

    <div class="relative overflow-x-auto">
      <!-- Mobile view (card layout) -->
      <div class="md:hidden space-y-4">
        {#if loading}
          <div class="p-4 text-center">Caricamento...</div>
        {:else if filteredComuni.length === 0}
          <div class="p-4 text-center">Nessun comune trovato</div>
        {:else}
          {#each filteredComuni as comune}
            <div class="bg-card rounded-lg shadow p-4 space-y-2">
              <div class="flex justify-between items-start">
                <div class="flex items-center gap-4">
                  <div class="relative">
                    <Avatar
                      class="h-12 w-12 cursor-pointer"
                      onclick={() => handleImageClick(getComuneImage(comune))}
                    >
                      <AvatarImage src={getComuneImage(comune)} alt={comune.nome} />
                      <AvatarFallback>{comune.nome?.[0]?.toUpperCase()}</AvatarFallback>
                    </Avatar>
                  </div>
                  <div>
                    <h3 class="font-medium">{comune.nome}</h3>
                    <p class="text-sm text-muted-foreground">{comune.provincia}</p>
                  </div>
                </div>
                <div class="flex space-x-2">
                  <Button variant="ghost" size="icon" onclick={() => handleGiunta(comune)}>
                    <Users size={16} />
                  </Button>
                  <Button variant="ghost" size="icon" onclick={() => handleEdit(comune)}>
                    <Edit size={16} />
                  </Button>
                  <Button variant="ghost" size="icon" onclick={() => handleDelete(comune.id)}>
                    <Trash2 size={16} />
                  </Button>
                </div>
              </div>
              <div class="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span class="text-muted-foreground">Tel:</span> {comune.telefono}
                </div>
                <div>
                  <span class="text-muted-foreground">Tel Vigili:</span> {comune.telefono_vigili}
                </div>
                <div>
                  <span class="text-muted-foreground">Sequenza:</span> {comune.sequenza_visualizzazione}
                </div>
              </div>
            </div>
          {/each}
        {/if}
      </div>

      <!-- Desktop view (table layout) -->
      <table class="w-full text-sm text-left hidden md:table">
        <thead class="text-sm bg-muted">
          <tr>
            <th class="px-6 py-3">Immagine</th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('nome')}
              >
                <span>Nome</span>
                <span class="text-muted-foreground">{getSortIcon('nome')}</span>
              </button>
            </th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('provincia')}
              >
                <span>Provincia</span>
                <span class="text-muted-foreground">{getSortIcon('provincia')}</span>
              </button>
            </th>
            <th class="px-6 py-3">Telefono</th>
            <th class="px-6 py-3">Telefono Vigili</th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('sequenza_visualizzazione')}
              >
                <span>Sequenza</span>
                <span class="text-muted-foreground">{getSortIcon('sequenza_visualizzazione')}</span>
              </button>
            </th>
            <th class="px-6 py-3 text-right">Azioni</th>
          </tr>
        </thead>
        <tbody>
          {#if loading}
            <tr>
              <td colspan="7" class="px-6 py-4 text-center">Caricamento...</td>
            </tr>
          {:else if filteredComuni.length === 0}
            <tr>
              <td colspan="7" class="px-6 py-4 text-center">Nessun comune trovato</td>
            </tr>
          {:else}
            {#each filteredComuni as comune}
              <tr class="border-b hover:bg-muted/50">
                <td class="px-6 py-4">
                  <div class="relative inline-block">
                    <Avatar
                      class="h-12 w-12 cursor-pointer"
                      onclick={() => handleImageClick(getComuneImage(comune))}
                    >
                      <AvatarImage src={getComuneImage(comune)} alt={comune.nome} />
                      <AvatarFallback>{comune.nome?.[0]?.toUpperCase()}</AvatarFallback>
                    </Avatar>
                  </div>
                </td>
                <td class="px-6 py-4">{comune.nome}</td>
                <td class="px-6 py-4">{comune.provincia}</td>
                <td class="px-6 py-4">{comune.telefono}</td>
                <td class="px-6 py-4">{comune.telefono_vigili}</td>
                <td class="px-6 py-4">{comune.sequenza_visualizzazione}</td>
                <td class="px-6 py-4 text-right space-x-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleGiunta(comune)}
                  >
                    <Users size={16} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleEdit(comune)}
                  >
                    <Edit size={16} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleDelete(comune.id)}
                  >
                    <Trash2 size={16} />
                  </Button>
                </td>
              </tr>
            {/each}
          {/if}
        </tbody>
      </table>
    </div>
  </DataTable>

  <EntityDialog
    open={dialogOpen}
    title={editingComune ? "Modifica comune" : "Nuovo comune"}
    description={editingComune ? "Modifica i dati del comune" : "Inserisci i dati del nuovo comune"}
    fields={[
      { key: "nome", label: "Nome", required: true, value: editingComune?.nome ?? "" },
      { key: "provincia", label: "Provincia", required: true, value: editingComune?.provincia ?? "" },
      { key: "telefono", label: "Telefono", value: editingComune?.telefono ?? "" },
      { key: "telefono_vigili", label: "Telefono Vigili", value: editingComune?.telefono_vigili ?? "" },
      { key: "latitude", label: "Latitudine", type: "number", value: editingComune?.latitude ?? null },
      { key: "longitude", label: "Longitudine", type: "number", value: editingComune?.longitude ?? null },
      { key: "sequenza_visualizzazione", label: "Sequenza", type: "number", value: editingComune?.sequenza_visualizzazione ?? 0 },
      { key: "categorie_da_escludere", label: "Categorie da escludere", value: editingComune?.categorie_da_escludere ?? "" }
    ]}
    onSubmit={handleSubmit}
    onCancel={() => dialogOpen = false}
    loading={saving}
  />
</AdminLayout>

<Dialog open={imagePreviewOpen} onOpenChange={(open) => imagePreviewOpen = open}>
  <DialogContent class="max-w-3xl">
    <DialogHeader>
      <DialogTitle>Anteprima immagine</DialogTitle>
    </DialogHeader>
    <div class="flex flex-col gap-4">
      <img src={previewingImage} alt="Preview" class="w-full rounded-lg" />
      <div class="flex justify-center">
        <div>
          <input
            type="file"
            accept="image/jpeg"
            class="hidden"
            id="imageUpload"
            onchange={(e) => {
              const comune = comuni.find(c => getComuneImage(c) === previewingImage);
              if (comune) {
                handleImageUpload(e, comune);
                imagePreviewOpen = false;
              }
            }}
          />
          <label for="imageUpload" class="cursor-pointer">
            <Button disabled={imageUploading}>
              <Upload class="mr-2 h-4 w-4" />
              {imageUploading ? 'Caricamento...' : 'Sostituisci'}
            </Button>
          </label>
        </div>
      </div>
    </div>
  </DialogContent>
</Dialog>