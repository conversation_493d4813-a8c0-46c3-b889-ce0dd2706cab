<script lang="ts">
  import AdminLayout from "$lib/components/ui/layout/AdminLayout.svelte";
  import { push } from "svelte-spa-router";
  import { ArrowLeft, User } from "lucide-svelte";
  import { Button } from "$lib/components/ui/button";
  import { Card, CardContent, CardHeader, CardTitle } from "$lib/components/ui/card";
  import { supabase } from "$lib/supabase/supabaseClient";
  import { toast } from "svelte-sonner";
  import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "$lib/components/ui/tabs/index.js";
  import type { Database } from "$lib/supabase/database.types";


  // Props
  let { params } = $props<{ params: { email: string } }>();
  const userEmail = decodeURIComponent(params.email);

  // State
  type TableRecord = {
    id: number;
    name?: string;
    title?: string;
    description?: string;
    creato_il?: string | null;
    modificato_il?: string | null;
  };

  type TableData = {
    tableName: TableName;
    displayName: string;
    created: TableRecord[];
    modified: TableRecord[];
  };

  let tableData = $state<TableData[]>([]);
  let loading = $state(true);
  let activeTab = $state("created");

  // Table configuration
  type TableName = keyof Database['public']['Tables'];

  type TableConfig = {
    name: TableName;
    displayName: string;
    nameField: string;
  };

  const tables: TableConfig[] = [
    { name: 'categorie', displayName: 'Categorie', nameField: 'nome' },
    { name: 'comuni', displayName: 'Comuni', nameField: 'nome' },
    { name: 'eventi', displayName: 'Eventi', nameField: 'titolo' },
    { name: 'servizi', displayName: 'Servizi', nameField: 'nome' },
    { name: 'giunte_comunali', displayName: 'Giunte Comunali', nameField: 'nome' },
    { name: 'indirizzi', displayName: 'Indirizzi', nameField: 'indirizzo' },
    { name: 'orari', displayName: 'Orari', nameField: 'giorno' }
  ];

  // Load data
  async function loadUserData() {
    try {
      loading = true;

      const results: TableData[] = [];

      for (const table of tables) {
        // Get records created by this user
        const { data: createdData, error: createdError } = await supabase
          .from(table.name)
          .select('*')
          .eq('creato_da', userEmail);

        if (createdError) throw createdError;

        // Get records modified by this user
        const { data: modifiedData, error: modifiedError } = await supabase
          .from(table.name)
          .select('*')
          .eq('modificato_da', userEmail);

        if (modifiedError) throw modifiedError;

        // Format the data
        const formattedCreated = (createdData || []).map(record => {
          const formattedRecord: TableRecord = {
            id: Number(record.id),
            name: (record as any)[table.nameField] || `ID: ${record.id}`,
            description: (record as any).descrizione || '',
            creato_il: (record as any).creato_il,
            modificato_il: (record as any).modificato_il
          };
          return formattedRecord;
        });

        const formattedModified = (modifiedData || []).map(record => {
          const formattedRecord: TableRecord = {
            id: Number(record.id),
            name: (record as any)[table.nameField] || `ID: ${record.id}`,
            description: (record as any).descrizione || '',
            creato_il: (record as any).creato_il,
            modificato_il: (record as any).modificato_il
          };
          return formattedRecord;
        });

        results.push({
          tableName: table.name,
          displayName: table.displayName,
          created: formattedCreated,
          modified: formattedModified
        });
      }

      tableData = results;
    } catch (error) {
      console.error('Error loading user data:', error);
      toast.error("Errore nel caricamento dei dati dell'utente");
    } finally {
      loading = false;
    }
  }

  function formatDate(dateString: string | null | undefined) {
    if (!dateString) return 'N/D';
    return new Date(dateString).toLocaleString('it-IT');
  }

  function handleBack() {
    push('/amministrazione');
  }



  $effect(() => {
    loadUserData();
  });
</script>

<AdminLayout>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <Button variant="ghost" class="mb-4" onclick={handleBack}>
          <ArrowLeft class="mr-2 h-4 w-4" />
          Torna all'amministrazione
        </Button>
        <h1 class="text-3xl font-bold tracking-tight flex items-center gap-2">
          <User class="h-8 w-8" />
          Dettaglio Utente
        </h1>
        <p class="text-muted-foreground">{userEmail}</p>
      </div>
    </div>

    {#if loading}
      <Card>
        <CardContent class="flex justify-center items-center h-64">
          <p class="text-muted-foreground">Caricamento dati...</p>
        </CardContent>
      </Card>
    {:else}
      <Tabs value={activeTab} onValueChange={(value: string) => activeTab = value} class="w-full">
        <TabsList class="grid w-full grid-cols-2">
          <TabsTrigger value="created" class="">Creati</TabsTrigger>
          <TabsTrigger value="modified" class="">Modificati</TabsTrigger>
        </TabsList>

        <TabsContent value="created" class="">
          {#if tableData.every(table => table.created.length === 0)}
            <Card>
              <CardContent class="flex justify-center items-center h-32">
                <p class="text-muted-foreground">Nessun record creato da questo utente</p>
              </CardContent>
            </Card>
          {:else}
            {#each tableData.filter(table => table.created.length > 0) as table}
              <Card class="mb-4">
                <CardHeader>
                  <CardTitle>{table.displayName}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div class="relative overflow-x-auto">
                    <!-- Mobile view (card layout) -->
                    <div class="md:hidden space-y-4">
                      {#each table.created as record}
                        <div class="bg-muted/30 rounded-lg border shadow p-4 space-y-2">
                          <div>
                            <h3 class="font-medium">{record.name}</h3>
                          </div>
                          <div class="grid grid-cols-2 gap-2 text-sm">
                            <div>
                              <span class="text-muted-foreground">Creato il:</span> {formatDate(record.creato_il)}
                            </div>
                            <div>
                              <span class="text-muted-foreground">Modificato il:</span> {formatDate(record.modificato_il)}
                            </div>
                          </div>
                        </div>
                      {/each}
                    </div>

                    <!-- Desktop view (table layout) -->
                    <table class="w-full text-sm text-left hidden md:table">
                      <thead class="text-sm bg-muted">
                        <tr>
                          <th class="px-6 py-3">Nome</th>
                          <th class="px-6 py-3">Creato il</th>
                          <th class="px-6 py-3">Modificato il</th>
                        </tr>
                      </thead>
                      <tbody>
                        {#each table.created as record}
                          <tr class="border-b hover:bg-muted/50">
                            <td class="px-6 py-4">{record.name}</td>
                            <td class="px-6 py-4">{formatDate(record.creato_il)}</td>
                            <td class="px-6 py-4">{formatDate(record.modificato_il)}</td>
                          </tr>
                        {/each}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            {/each}
          {/if}
        </TabsContent>

        <TabsContent value="modified" class="">
          {#if tableData.every(table => table.modified.length === 0)}
            <Card>
              <CardContent class="flex justify-center items-center h-32">
                <p class="text-muted-foreground">Nessun record modificato da questo utente</p>
              </CardContent>
            </Card>
          {:else}
            {#each tableData.filter(table => table.modified.length > 0) as table}
              <Card class="mb-4">
                <CardHeader>
                  <CardTitle>{table.displayName}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div class="relative overflow-x-auto">
                    <!-- Mobile view (card layout) -->
                    <div class="md:hidden space-y-4">
                      {#each table.modified as record}
                        <div class="bg-muted/30 rounded-lg border shadow p-4 space-y-2">
                          <div>
                            <h3 class="font-medium">{record.name}</h3>
                          </div>
                          <div class="grid grid-cols-2 gap-2 text-sm">
                            <div>
                              <span class="text-muted-foreground">Creato il:</span> {formatDate(record.creato_il)}
                            </div>
                            <div>
                              <span class="text-muted-foreground">Modificato il:</span> {formatDate(record.modificato_il)}
                            </div>
                          </div>
                        </div>
                      {/each}
                    </div>

                    <!-- Desktop view (table layout) -->
                    <table class="w-full text-sm text-left hidden md:table">
                      <thead class="text-sm bg-muted">
                        <tr>
                          <th class="px-6 py-3">Nome</th>
                          <th class="px-6 py-3">Creato il</th>
                          <th class="px-6 py-3">Modificato il</th>
                        </tr>
                      </thead>
                      <tbody>
                        {#each table.modified as record}
                          <tr class="border-b hover:bg-muted/50">
                            <td class="px-6 py-4">{record.name}</td>
                            <td class="px-6 py-4">{formatDate(record.creato_il)}</td>
                            <td class="px-6 py-4">{formatDate(record.modificato_il)}</td>
                          </tr>
                        {/each}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            {/each}
          {/if}
        </TabsContent>
      </Tabs>
    {/if}
  </div>
</AdminLayout>
