<script lang="ts">
  import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle } from "$lib/components/ui/dialog";
  import { Button } from "$lib/components/ui/button";
  import { ChevronLeft, ChevronRight, Upload, Trash2, X } from "lucide-svelte";
  import { StorageService } from '$lib/supabase/storage';
  import { toast } from "svelte-sonner";
  import type { Tables } from "$lib/supabase/database.types";

  type Servizio = Tables<'servizi'>;

  interface ServicePhoto {
    name: string;
    url: string;
    number: number;
  }

  let { 
    open = $bindable(false), 
    servizio = $bindable<Servizio | null>(null),
    onPhotoChange
  } = $props<{
    open: boolean;
    servizio: Servizio | null;
    onPhotoChange?: () => void;
  }>();

  const storageService = new StorageService('servizi');

  let photos = $state<ServicePhoto[]>([]);
  let currentPhotoIndex = $state(0);
  let loading = $state(false);
  let uploading = $state(false);
  let deleting = $state(false);

  $effect(() => {
    if (open && servizio) {
      loadPhotos();
    }
  });

  async function loadPhotos() {
    if (!servizio?.nome) return;
    
    try {
      loading = true;
      photos = await storageService.getServicePhotos(servizio.nome);
      currentPhotoIndex = 0;
    } catch (error) {
      console.error('Error loading photos:', error);
      toast.error("Errore nel caricamento delle foto");
    } finally {
      loading = false;
    }
  }

  function nextPhoto() {
    if (photos.length > 0) {
      currentPhotoIndex = (currentPhotoIndex + 1) % photos.length;
    }
  }

  function prevPhoto() {
    if (photos.length > 0) {
      currentPhotoIndex = currentPhotoIndex === 0 ? photos.length - 1 : currentPhotoIndex - 1;
    }
  }

  async function handlePhotoUpload(event: Event) {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    if (!file || !servizio?.nome) return;

    try {
      uploading = true;
      const nextNumber = await storageService.getNextPhotoNumber(servizio.nome);
      const folderPath = `${servizio.nome}/${servizio.nome}-${nextNumber}.jpg`;
      
      await storageService.uploadFile(file, folderPath);
      await loadPhotos();
      
      // Navigate to the newly uploaded photo
      currentPhotoIndex = photos.length - 1;
      
      toast.success("Foto caricata con successo");
      onPhotoChange?.();
    } catch (error) {
      console.error('Upload error:', error);
      toast.error("Errore nel caricamento della foto");
    } finally {
      uploading = false;
      input.value = '';
    }
  }

  async function handlePhotoDelete() {
    if (!servizio?.nome || photos.length === 0) return;

    const currentPhoto = photos[currentPhotoIndex];
    if (!currentPhoto) return;

    try {
      deleting = true;
      const filePath = `${servizio.nome}/${currentPhoto.name}`;
      await storageService.deleteFile(filePath);
      
      await loadPhotos();
      
      // Adjust current index if needed
      if (currentPhotoIndex >= photos.length && photos.length > 0) {
        currentPhotoIndex = photos.length - 1;
      }
      
      toast.success("Foto eliminata con successo");
      onPhotoChange?.();
    } catch (error) {
      console.error('Delete error:', error);
      toast.error("Errore nell'eliminazione della foto");
    } finally {
      deleting = false;
    }
  }

  function handleClose() {
    open = false;
  }

  let currentPhoto = $derived(photos[currentPhotoIndex]);
</script>

<Dialog {open} onOpenChange={(newOpen) => open = newOpen}>
  <DialogContent class="max-w-4xl max-h-[90vh] overflow-hidden">
    <DialogHeader>
      <DialogTitle class="flex items-center justify-between">
        <span>Galleria foto - {servizio?.nome}</span>
        <Button variant="ghost" size="icon" onclick={handleClose}>
          <X class="h-4 w-4" />
        </Button>
      </DialogTitle>
    </DialogHeader>

    <div class="flex flex-col gap-4 h-full">
      {#if loading}
        <div class="flex items-center justify-center h-64">
          <div class="text-center">Caricamento foto...</div>
        </div>
      {:else if photos.length === 0}
        <div class="flex flex-col items-center justify-center h-64 gap-4">
          <div class="text-center text-muted-foreground">
            Nessuna foto trovata per questo servizio
          </div>
          <div>
            <input
              type="file"
              accept="image/jpeg"
              class="hidden"
              id="photoUpload"
              onchange={handlePhotoUpload}
              disabled={uploading}
            />
            <Button
              variant="outline"
              class="gap-2"
              onclick={() => document.getElementById('photoUpload')?.click()}
              disabled={uploading}
            >
              <Upload class="h-4 w-4" />
              {uploading ? 'Caricamento...' : 'Carica prima foto'}
            </Button>
          </div>
        </div>
      {:else}
        <!-- Photo viewer -->
        <div class="relative flex-1 flex items-center justify-center bg-muted/20 rounded-lg overflow-hidden">
          <img 
            src={currentPhoto.url} 
            alt={`${servizio?.nome} - Foto ${currentPhoto.number}`}
            class="max-w-full max-h-full object-contain"
          />
          
          <!-- Navigation arrows -->
          {#if photos.length > 1}
            <Button
              variant="ghost"
              size="icon"
              class="absolute left-2 top-1/2 -translate-y-1/2 bg-background/80 hover:bg-background"
              onclick={prevPhoto}
            >
              <ChevronLeft class="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              class="absolute right-2 top-1/2 -translate-y-1/2 bg-background/80 hover:bg-background"
              onclick={nextPhoto}
            >
              <ChevronRight class="h-4 w-4" />
            </Button>
          {/if}
        </div>

        <!-- Photo info and controls -->
        <div class="flex items-center justify-between gap-4">
          <div class="text-sm text-muted-foreground">
            Foto {currentPhotoIndex + 1} di {photos.length}
          </div>
          
          <div class="flex gap-2">
            <input
              type="file"
              accept="image/jpeg"
              class="hidden"
              id="photoUpload"
              onchange={handlePhotoUpload}
              disabled={uploading}
            />
            <Button
              variant="outline"
              size="sm"
              class="gap-2"
              onclick={() => document.getElementById('photoUpload')?.click()}
              disabled={uploading}
            >
              <Upload class="h-4 w-4" />
              {uploading ? 'Caricamento...' : 'Aggiungi foto'}
            </Button>
            
            <Button
              variant="destructive"
              size="sm"
              class="gap-2"
              onclick={handlePhotoDelete}
              disabled={deleting}
            >
              <Trash2 class="h-4 w-4" />
              {deleting ? 'Eliminazione...' : 'Elimina foto'}
            </Button>
          </div>
        </div>

        <!-- Photo thumbnails -->
        {#if photos.length > 1}
          <div class="flex gap-2 overflow-x-auto pb-2">
            {#each photos as photo, index}
              <button
                class="flex-shrink-0 w-16 h-16 rounded border-2 overflow-hidden {index === currentPhotoIndex ? 'border-primary' : 'border-muted'}"
                onclick={() => currentPhotoIndex = index}
              >
                <img 
                  src={photo.url} 
                  alt={`Foto ${photo.number}`}
                  class="w-full h-full object-cover"
                />
              </button>
            {/each}
          </div>
        {/if}
      {/if}
    </div>
  </DialogContent>
</Dialog>
