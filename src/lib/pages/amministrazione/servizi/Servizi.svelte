<script lang="ts">
  import AdminLayout from "$lib/components/ui/layout/AdminLayout.svelte";
  import DataTable from "$lib/components/ui/data-table/DataTable.svelte";
  import EntityDialog from "$lib/components/ui/dialog/EntityDialog.svelte";
  import IndirizziDialog from "./IndirizziDialog.svelte";
  import OrariDialog from "./OrariDialog.svelte";
  import PhotoGalleryDialog from "./PhotoGalleryDialog.svelte";
  import { Button } from "$lib/components/ui/button";
  import { CrudService } from "$lib/supabase/crud";
  import { toast } from "svelte-sonner";
  import { Edit, Trash2, MapPin, Clock, ArrowLeft } from "lucide-svelte";
  import type { Tables } from "$lib/supabase/database.types";
  import { push } from "svelte-spa-router";
  import { Select, SelectTrigger, SelectContent, SelectItem } from "$lib/components/ui/select";
  import { StorageService } from '$lib/supabase/storage';
  import { Avatar, AvatarImage, AvatarFallback } from "$lib/components/ui/avatar/index.js";

  type Servizio = Tables<'servizi'>;
  type Indirizzo = Tables<'indirizzi'>;
  type Orario = Tables<'orari'>;
  type Categoria = Tables<'categorie'>;

  const serviziService = new CrudService('servizi');
  const indirizziService = new CrudService('indirizzi');
  const orariService = new CrudService('orari');
  const categorieService = new CrudService('categorie');
  const storageService = new StorageService('servizi');

  let servizi = $state<Servizio[]>([]);
  let categorie = $state<Categoria[]>([]);
  let loading = $state(true);
  let dialogOpen = $state(false);
  let indirizziDialogOpen = $state(false);
  let orariDialogOpen = $state(false);
  let photoGalleryOpen = $state(false);
  let editingServizio = $state<Servizio | null>(null);
  let editingIndirizzo = $state<Indirizzo | null>(null);
  let editingOrario = $state<Orario | null>(null);
  let selectedServizioForPhotos = $state<Servizio | null>(null);
  let saving = $state(false);
  let searchQuery = $state('');
  let sortField = $state<keyof Servizio | null>(null);
  let sortDirection = $state<'asc' | 'desc'>('asc');
  let filteredServizi = $derived(getFilteredServizi());
  let imageTimestamps = $state<Record<string, number>>({});

  let { params } = $props<{ params?: { id: string } }>();

  let categoria = $state<Categoria | null>(null);

  async function loadServizi() {
    try {
      loading = true;

      // Get all categories
      const categorieData = await categorieService.getAll();
      categorie = categorieData;

      if (params?.id) {
        const categoriaId = parseInt(params.id);
        if (isNaN(categoriaId)) {
          toast.error("ID categoria non valido");
          push('/amministrazione/categorie');
          return;
        }

        categoria = categorie.find(c => c.id === categoriaId) || null;
        if (!categoria) {
          toast.error("Categoria non trovata");
          push('/amministrazione/categorie');
          return;
        }

        // Use the getByField method to directly fetch services by category ID
        // This is more efficient than fetching all services and filtering in the frontend
        const serviziByCategory = await serviziService.getByField('categoria_id', categoriaId);
        servizi = serviziByCategory;
      } else {
        // If no category filter, get all services
        const serviziData = await serviziService.getAll();
        servizi = serviziData;
      }
    } catch (error) {
      toast.error("Errore nel caricamento dei dati");
      if (params?.id) push('/amministrazione/categorie');
    } finally {
      loading = false;
    }
  }

  $effect(() => {
    loadServizi();
  });

  function handleAdd() {
    editingServizio = null;
    dialogOpen = true;
  }

  function handleEdit(servizio: Servizio) {
    editingServizio = servizio;
    dialogOpen = true;
  }

  async function handleDelete(id: number) {
    try {
      await serviziService.delete(id);
      toast.success("Servizio eliminato con successo");
      await loadServizi();
    } catch (error) {
      toast.error("Errore nell'eliminazione del servizio");
    }
  }

  async function handleSubmit(data: Record<string, any>) {
    try {
      saving = true;
      const payload = {
        ...data,
        categoria_id: params?.id ? parseInt(params.id) : data.categoria_id
      };

      if (editingServizio) {
        await serviziService.update(editingServizio.id, payload);
        toast.success("Servizio aggiornato con successo");
      } else {
        await serviziService.create(payload);
        toast.success("Servizio creato con successo");
      }
      dialogOpen = false;
      await loadServizi();
    } catch (error) {
      toast.error("Errore nel salvataggio del servizio");
    } finally {
      saving = false;
    }
  }

  async function handleIndirizzi(servizio: Servizio) {
    try {
      // Use getByField instead of getAll for more efficient querying
      const indirizzi = await indirizziService.getByField('servizio_id', servizio.id);

      editingServizio = servizio;
      // Take the first matching address if any exists
      editingIndirizzo = indirizzi.length > 0 ? indirizzi[0] : null;
      indirizziDialogOpen = true;
    } catch (error) {
      console.error('Error loading address:', error);
      toast.error("Errore nel caricamento dell'indirizzo");
    }
  }

  async function handleOrari(servizio: Servizio) {
    try {
      // Use getByField instead of getAll for more efficient querying
      const orari = await orariService.getByField('servizio_id', servizio.id);

      editingServizio = servizio;
      // Take the first matching schedule if any exists
      editingOrario = orari.length > 0 ? orari[0] : null;
      orariDialogOpen = true;
    } catch (error) {
      console.error('Error loading hours:', error);
      toast.error("Errore nel caricamento degli orari");
    }
  }

  function handleDialogClose() {
    indirizziDialogOpen = false;
    orariDialogOpen = false;
    editingIndirizzo = null;
    editingOrario = null;
    editingServizio = null;
  }

  function handleSearch(event: Event) {
    const target = event.target as HTMLInputElement;
    searchQuery = target.value;
  }

  function getCategoryName(categoryId: number | null) {
    if (!categoryId) return 'N/D';
    return categorie.find(cat => cat.id === categoryId)?.nome ?? 'N/D';
  }

  function handleBack() {
    push('/amministrazione/categorie');
  }

  function handleSort(field: keyof Servizio) {
    if (sortField === field) {
      sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      sortField = field;
      sortDirection = 'asc';
    }
  }

  function getSortIcon(field: keyof Servizio) {
    if (sortField !== field) return '↕';
    return sortDirection === 'asc' ? '↑' : '↓';
  }

  function getSortOptions() {
    return [
      { value: 'nome', label: 'Nome' },
      { value: 'tipologia', label: 'Tipologia' },
      { value: 'categoria_id', label: 'Categoria' }
    ];
  }

  function getFilteredServizi(): Servizio[] {
    let filtered = !searchQuery.trim()
      ? servizi
      : servizi.filter(servizio => {
          const query = searchQuery.toLowerCase();
          return (
            (servizio.nome?.toLowerCase() || '').includes(query) ||
            (servizio.tipologia?.toLowerCase() || '').includes(query) ||
            (servizio.descrizione?.toLowerCase() || '').includes(query)
          );
        });

    if (sortField) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = sortField ? a[sortField] : null;
        const bValue = sortField ? b[sortField] : null;

        if (aValue === null || aValue === undefined) return sortDirection === 'asc' ? 1 : -1;
        if (bValue === null || bValue === undefined) return sortDirection === 'asc' ? -1 : 1;

        const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }

    return filtered;
  }

  function getServiceImage(servizio: Servizio) {
    if (!servizio?.id || !servizio.nome) return '';
    const timestamp = imageTimestamps[servizio.id.toString()] || '';
    const folderPath = `${servizio.nome}/${servizio.nome}-1.jpg`;
    return storageService.getImageUrl(folderPath) + (timestamp ? `?t=${timestamp}` : '');
  }

  function handleImageClick(servizio: Servizio) {
    selectedServizioForPhotos = servizio;
    photoGalleryOpen = true;
  }

  function handlePhotoChange() {
    // Update timestamp to force image refresh
    if (selectedServizioForPhotos?.id) {
      imageTimestamps[selectedServizioForPhotos.id.toString()] = Date.now();
    }
  }
</script>

<AdminLayout>
  {#if params?.id}
    <div class="mb-4">
      <Button variant="ghost" onclick={handleBack}>
        <ArrowLeft class="mr-2 h-4 w-4" />
        Torna alle categorie
      </Button>
    </div>
  {/if}

  <DataTable
    title={categoria ? `Servizi - ${categoria.nome}` : "Servizi"}
    description={categoria ? `Gestisci i servizi della categoria ${categoria.nome}` : "Gestisci i servizi del territorio"}
    onAdd={handleAdd}
    searchPlaceholder="Cerca servizi..."
    onSearch={handleSearch}
  >
    {#snippet actions()}
      <Select
        type="single"
        value={sortField || ''}
        onValueChange={(value: string) => handleSort(value as keyof Servizio)}
      >
        <SelectTrigger class="w-[150px] bg-transparent border-none shadow-none hover:bg-accent hover:text-accent-foreground focus:ring-0">
          {sortField
            ? `${getSortOptions().find(opt => opt.value === sortField)?.label} ${sortDirection === 'asc' ? '↑' : '↓'}`
            : 'Ordina per...'
          }
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="">Nessun ordinamento</SelectItem>
          {#each getSortOptions() as option}
            <SelectItem value={option.value}>
              {option.label}
            </SelectItem>
          {/each}
        </SelectContent>
      </Select>
    {/snippet}

    <div class="relative overflow-x-auto">
      <!-- Mobile view (card layout) -->
      <div class="md:hidden space-y-4">
        {#if loading}
          <div class="p-4 text-center">Caricamento...</div>
        {:else if filteredServizi.length === 0}
          <div class="p-4 text-center">Nessun servizio trovato</div>
        {:else}
          {#each filteredServizi as servizio}
            <div class="bg-card rounded-lg shadow p-4 space-y-2">
              <div class="flex justify-between items-start">
                <div class="flex items-center gap-4">
                  <div class="relative">
                    <Avatar
                      class="h-12 w-12 cursor-pointer"
                      onclick={() => handleImageClick(servizio)}
                    >
                      <AvatarImage src={getServiceImage(servizio)} alt={servizio.nome} />
                      <AvatarFallback>{servizio.nome?.[0]?.toUpperCase()}</AvatarFallback>
                    </Avatar>
                  </div>
                  <div>
                    <h3 class="font-medium">{servizio.nome}</h3>
                    <p class="text-sm text-muted-foreground">Categoria: <span class="text-foreground">{getCategoryName(servizio.categoria_id)}</span></p>
                  </div>
                </div>
                <div class="flex space-x-2">
                  <Button variant="ghost" size="icon" onclick={() => handleIndirizzi(servizio)}>
                    <MapPin size={16} />
                  </Button>
                  <Button variant="ghost" size="icon" onclick={() => handleOrari(servizio)}>
                    <Clock size={16} />
                  </Button>
                  <Button variant="ghost" size="icon" onclick={() => handleEdit(servizio)}>
                    <Edit size={16} />
                  </Button>
                  <Button variant="ghost" size="icon" onclick={() => handleDelete(servizio.id)}>
                    <Trash2 size={16} />
                  </Button>
                </div>
              </div>
              <div class="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span class="text-muted-foreground">Tipologia:</span><br />
                  {servizio.tipologia}
                </div>
                <div>
                  <span class="text-muted-foreground">Premium:</span><br />
                  {servizio.premium ? "Sì" : "No"}
                </div>
              </div>
            </div>
          {/each}
        {/if}
      </div>

      <!-- Desktop view (table layout) -->
      <table class="w-full text-sm text-left hidden md:table">
        <thead class="text-sm bg-muted">
          <tr>
            <th class="px-6 py-3">Immagine</th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('nome')}
              >
                <span>Nome</span>
                <span class="text-muted-foreground">{getSortIcon('nome')}</span>
              </button>
            </th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('categoria_id')}
              >
                <span>Categoria</span>
                <span class="text-muted-foreground">{getSortIcon('categoria_id')}</span>
              </button>
            </th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('tipologia')}
              >
                <span>Tipologia</span>
                <span class="text-muted-foreground">{getSortIcon('tipologia')}</span>
              </button>
            </th>
            <th class="px-6 py-3">Premium</th>
            <th class="px-6 py-3 text-right">Azioni</th>
          </tr>
        </thead>
        <tbody>
          {#if loading}
            <tr>
              <td colspan="5" class="px-6 py-4 text-center">Caricamento...</td>
            </tr>
          {:else if servizi.length === 0}
            <tr>
              <td colspan="5" class="px-6 py-4 text-center">Nessun servizio trovato</td>
            </tr>
          {:else}
            {#each filteredServizi as servizio}
              <tr class="border-b hover:bg-muted/50">
                <td class="px-6 py-4">
                  <div class="relative inline-block">
                    <Avatar
                      class="h-12 w-12 cursor-pointer"
                      onclick={() => handleImageClick(servizio)}
                    >
                      <AvatarImage src={getServiceImage(servizio)} alt={servizio.nome} />
                      <AvatarFallback>{servizio.nome?.[0]?.toUpperCase()}</AvatarFallback>
                    </Avatar>
                  </div>
                </td>
                <td class="px-6 py-4">{servizio.nome}</td>
                <td class="px-6 py-4">{getCategoryName(servizio.categoria_id)}</td>
                <td class="px-6 py-4">{servizio.tipologia}</td>
                <td class="px-6 py-4">{servizio.premium ? "Sì" : "No"}</td>
                <td class="px-6 py-4 text-right space-x-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleIndirizzi(servizio)}
                  >
                    <MapPin size={16} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleOrari(servizio)}
                  >
                    <Clock size={16} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleEdit(servizio)}
                  >
                    <Edit size={16} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleDelete(servizio.id)}
                  >
                    <Trash2 size={16} />
                  </Button>
                </td>
              </tr>
            {/each}
          {/if}
        </tbody>
      </table>
    </div>
  </DataTable>

  <EntityDialog
    open={dialogOpen}
    title={editingServizio ? "Modifica servizio" : "Nuovo servizio"}
    description={editingServizio ? "Modifica i dati del servizio" : "Inserisci i dati del nuovo servizio"}
    fields={[
      { key: "nome", label: "Nome", required: true, value: editingServizio?.nome ?? "" },
      { key: "descrizione", label: "Descrizione", value: editingServizio?.descrizione ?? "" },
      ...(params?.id ? [] : [{
        key: "categoria_id",
        label: "Categoria",
        type: "select",
        required: true,
        value: editingServizio?.categoria_id ?? "",
        options: categorie.map(cat => ({ value: cat.id, label: cat.nome }))
      }]),
      { key: "tipologia", label: "Tipologia", value: editingServizio?.tipologia ?? "" },
      { key: "telefono_primario", label: "Telefono primario", value: editingServizio?.telefono_primario ?? "" },
      { key: "telefono_secondario", label: "Telefono secondario", value: editingServizio?.telefono_secondario ?? "" },
      { key: "email", label: "Email", type: "email", value: editingServizio?.email ?? "" },
      { key: "sito_web", label: "Sito web", value: editingServizio?.sito_web ?? "" },
      { key: "dintorni_di", label: "Dintorni di", value: editingServizio?.dintorni_di ?? "" },
      { key: "per_il_turista", label: "Per il turista", type: "checkbox", value: editingServizio?.per_il_turista ?? false },
      { key: "premium", label: "Premium", type: "checkbox", value: editingServizio?.premium ?? false },
      { key: "in_evidenza_id", label: "In evidenza", type: "number", value: editingServizio?.in_evidenza_id ?? null }
    ]}
    onSubmit={handleSubmit}
    onCancel={() => dialogOpen = false}
    loading={saving}
  />

  <IndirizziDialog
    open={indirizziDialogOpen}
    servizioId={editingServizio?.id ?? 0}
    indirizzo={editingIndirizzo}
    onClose={handleDialogClose}
  />

  <OrariDialog
    open={orariDialogOpen}
    servizioId={editingServizio?.id ?? 0}
    orario={editingOrario}
    onClose={handleDialogClose}
  />

  <PhotoGalleryDialog
    bind:open={photoGalleryOpen}
    bind:servizio={selectedServizioForPhotos}
    onPhotoChange={handlePhotoChange}
  />
</AdminLayout>