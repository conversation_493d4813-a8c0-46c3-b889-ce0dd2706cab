<script lang="ts">
  import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "$lib/components/ui/dialog";
  import { Button } from "$lib/components/ui/button";
  import { Input } from "$lib/components/ui/input";
  import { Label } from "$lib/components/ui/label";
  import { CrudService } from "$lib/supabase/crud";
  import { toast } from "svelte-sonner";
  import type { Tables } from "$lib/supabase/database.types";

  type Orario = Tables<'orari'>;

  let {
    open = false,
    servizioId,
    orario = null,
    onClose
  } = $props<{
    open: boolean;
    servizioId: number;
    orario?: Orario | null;
    onClose: () => void;
  }>();

  const orariService = new CrudService('orari');
  let saving = $state(false);
  let formData = $state<Record<string, any>>({
    lunedì: '',
    martedì: '',
    mercoledì: '',
    gio<PERSON><PERSON>: '',
    venerdì: '',
    sabato: '',
    domenica: ''
  });

  $effect(() => {
    if (orario) {
      formData = { ...orario };
    } else {
      formData = {
        lunedì: '',
        martedì: '',
        mercoledì: '',
        giovedì: '',
        venerdì: '',
        sabato: '',
        domenica: '',
        servizio_id: servizioId
      };
    }
  });

  $effect(() => {
    if (servizioId === 0) {
      onClose();
    }
  });

  async function handleSubmit(e: SubmitEvent) {
    e.preventDefault();
    try {
      saving = true;
      if (orario) {
        await orariService.update(orario.id, formData);
        toast.success("Orari aggiornati con successo");
      } else {
        await orariService.create(formData);
        toast.success("Orari creati con successo");
      }
      onClose();
    } catch (error) {
      toast.error("Errore nel salvataggio degli orari");
    } finally {
      saving = false;
    }
  }

  const giorni = [
    'lunedì', 'martedì', 'mercoledì', 'giovedì', 'venerdì', 'sabato', 'domenica'
  ] as const;
</script>

<Dialog {open} onOpenChange={(isOpen) => !isOpen && onClose()}>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>{orario ? "Modifica orari" : "Nuovi orari"}</DialogTitle>
      <DialogDescription>
        {orario ? "Modifica gli orari di apertura" : "Inserisci gli orari di apertura"}
      </DialogDescription>
    </DialogHeader>

    <form onsubmit={handleSubmit} class="space-y-4">
      <div class="space-y-4">
        {#each giorni as giorno}
          <div class="space-y-2">
            <Label for={giorno}>{giorno.charAt(0).toUpperCase() + giorno.slice(1)}</Label>
            <Input
              id={giorno}
              placeholder="es: 9:00-13:00, 15:00-19:00"
              bind:value={formData[giorno]}
            />
          </div>
        {/each}
      </div>

      <DialogFooter>
        <Button type="button" variant="outline" onclick={onClose}>
          Annulla
        </Button>
        <Button type="submit" disabled={saving}>
          {saving ? "Salvataggio..." : "Salva"}
        </Button>
      </DialogFooter>
    </form>
  </DialogContent>
</Dialog>