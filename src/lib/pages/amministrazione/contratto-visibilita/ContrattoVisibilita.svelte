<script lang="ts">
  import { ContractService } from '$lib/services/contractService';
  import { toast } from "svelte-sonner";
  import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "$lib/components/ui/card";
  import { But<PERSON> } from "$lib/components/ui/button";
  import { Input } from "$lib/components/ui/input";
  import { Label } from "$lib/components/ui/label";
  import { Textarea } from "$lib/components/ui/textarea";

  const contractService = new ContractService('/assets/contratto-visibilita-template.docx');

  let isGenerating = $state(false);
  let formData = $state({
    identificativo: Date.now().toString().substring(0, 10),
    nomeCliente: '',
    cognomeCliente: '',
    nomeAzienda: '',
    ragioneSociale: '',
    viaCliente: '',
    numeroCivicoCliente: '',
    capCliente: '',
    paeseCliente: '',
    provinciaCliente: '',
    telefonoCliente: '',
    emailOPecCliente: '',
    pIvaOCfCliente: '',
    codiceUnivocoCliente: '',
    codiceIntermediarioCliente: '',
    paeseInserzione: '',
    paesiAddizionaliInserzione: '',
    categorieInserzione: '',
    indirizzoInserzione: '',
    telefonoInserzione: '',
    emailOPecInserzione: '',
    sitoWebInserzione: '',
    descrizioneAziendaInserzione: '',
    orariAperturaInserzione: '',
    noteInserzione: '',
    costoMensile: '',
    costoAnnuale: '',
    dataContratto: new Date().toISOString().split('T')[0]
  });

  async function handleSubmit(e: SubmitEvent) {
    e.preventDefault();
    isGenerating = true;

    try {
      // Create a copy of formData and convert the date
      const contractData = {
        ...formData,
        dataContratto: new Date(formData.dataContratto).toLocaleDateString('it-IT')
      };

      await contractService.downloadContract(
        contractData,
        `contratto-visibilita-${formData.identificativo || Date.now().toString().substring(0, 10)}.docx`
      );

      toast.success("Contratto generato con successo");
    } catch (error) {
      console.error('Error:', error);
      toast.error("Errore nella generazione del contratto");
    } finally {
      isGenerating = false;
    }
  }
</script>

<div class="container mx-auto py-8">
  <Card>
    <CardHeader>
      <CardTitle>Contratto di Visibilità</CardTitle>
      <CardDescription>
        Compila i dati per generare un nuovo contratto di visibilità
      </CardDescription>
    </CardHeader>
    <CardContent>
      <form onsubmit={handleSubmit} class="space-y-6 sm:space-y-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
          <!-- Cliente Info -->
            <div class="col-span-1 md:col-span-2">
                <h3 class="text-lg font-semibold">Dati Cliente</h3>
                <hr class="my-2 border-t border-gray-300" />
            </div>
          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="nomeCliente">Nome*</Label>
            <Input
              id="nomeCliente"
              bind:value={formData.nomeCliente}
              required
              placeholder="Nome del cliente"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="cognomeCliente">Cognome*</Label>
            <Input
              id="cognomeCliente"
              bind:value={formData.cognomeCliente}
              required
              placeholder="Cognome del cliente"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="nomeAzienda">Nome Azienda*</Label>
            <Input
              id="nomeAzienda"
              bind:value={formData.nomeAzienda}
              required
              placeholder="Nome dell'azienda"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="ragioneSociale">Ragione Sociale*</Label>
            <Input
              id="ragioneSociale"
              bind:value={formData.ragioneSociale}
              required
              placeholder="Ragione sociale"
            />
          </div>

          <!-- Indirizzo Cliente -->
          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="viaCliente">Via*</Label>
            <Input
              id="viaCliente"
              bind:value={formData.viaCliente}
              required
              placeholder="Via"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="numeroCivicoCliente">Numero Civico*</Label>
            <Input
              id="numeroCivicoCliente"
              bind:value={formData.numeroCivicoCliente}
              required
              placeholder="Numero civico"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="capCliente">CAP*</Label>
            <Input
              id="capCliente"
              bind:value={formData.capCliente}
              required
              placeholder="CAP"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="paeseCliente">Paese*</Label>
            <Input
              id="paeseCliente"
              bind:value={formData.paeseCliente}
              required
              placeholder="Paese"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="provinciaCliente">Provincia*</Label>
            <Input
              id="provinciaCliente"
              bind:value={formData.provinciaCliente}
              required
              placeholder="Provincia"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="telefonoCliente">Telefono*</Label>
            <Input
              id="telefonoCliente"
              type="tel"
              bind:value={formData.telefonoCliente}
              required
              placeholder="Numero di telefono"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="emailOPecCliente">Email/PEC*</Label>
            <Input
              id="emailOPecCliente"
              type="email"
              bind:value={formData.emailOPecCliente}
              required
              placeholder="Email o PEC"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="pIvaOCfCliente">P.IVA/Codice Fiscale*</Label>
            <Input
              id="pIvaOCfCliente"
              bind:value={formData.pIvaOCfCliente}
              required
              placeholder="Partita IVA o Codice Fiscale"
            />
          </div>
          
          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="codiceUnivocoCliente">Codice Univoco*</Label>
            <Input
              id="codiceUnivocoCliente"
              bind:value={formData.codiceUnivocoCliente}
              required
              placeholder="Codice Univoco"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="codiceIntermediarioCliente">Codice Intermediario</Label>
            <Input
              id="codiceIntermediarioCliente"
              bind:value={formData.codiceIntermediarioCliente}
              placeholder="Codice Intermediario"
            />
          </div>

          <!-- Inserzione Info -->
            <div class="col-span-1 md:col-span-2">
                <h3 class="text-lg font-semibold">Dati Inserzione</h3>
                <hr class="my-2 border-t border-gray-300" />
            </div>
          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="paeseInserzione">Paese*</Label>
            <Input
              id="paeseInserzione"
              bind:value={formData.paeseInserzione}
              required
              placeholder="Paese dell'inserzione"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="paesiAddizionaliInserzione">Paesi Addizionali</Label>
            <Input
              id="paesiAddizionaliInserzione"
              bind:value={formData.paesiAddizionaliInserzione}
              placeholder="Paesi addizionali"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="categorieInserzione">Categorie*</Label>
            <Input
              id="categorieInserzione"
              bind:value={formData.categorieInserzione}
              required
              placeholder="Categorie dell'inserzione"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="indirizzoInserzione">Indirizzo*</Label>
            <Input
              id="indirizzoInserzione"
              bind:value={formData.indirizzoInserzione}
              required
              placeholder="Indirizzo dell'inserzione"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="telefonoInserzione">Telefono*</Label>
            <Input
              id="telefonoInserzione"
              type="tel"
              bind:value={formData.telefonoInserzione}
              required
              placeholder="Telefono dell'inserzione"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="emailOPecInserzione">Email/PEC*</Label>
            <Input
              id="emailOPecInserzione"
              type="email"
              bind:value={formData.emailOPecInserzione}
              required
              placeholder="Email o PEC dell'inserzione"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="sitoWebInserzione">Sito Web</Label>
            <Input
              id="sitoWebInserzione"
              type="url"
              bind:value={formData.sitoWebInserzione}
              placeholder="Sito web"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0 col-span-1 md:col-span-2">
            <Label for="descrizioneAziendaInserzione">Descrizione Azienda</Label>
            <Textarea
              id="descrizioneAziendaInserzione"
              bind:value={formData.descrizioneAziendaInserzione}
              placeholder="Descrizione dell'azienda"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0 col-span-1 md:col-span-2">
            <Label for="orariAperturaInserzione">Orari di Apertura</Label>
            <Textarea
              id="orariAperturaInserzione"
              bind:value={formData.orariAperturaInserzione}
              placeholder="Orari di apertura"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0 col-span-1 md:col-span-2">
            <Label for="noteInserzione">Note</Label>
            <Textarea
              id="noteInserzione"
              bind:value={formData.noteInserzione}
              placeholder="Note aggiuntive"
            />
          </div>

          <!-- Contract Info -->
            <div class="col-span-1 md:col-span-2">
                <h3 class="text-lg font-semibold">Dati Contratto</h3>
                <hr class="my-2 border-t border-gray-300" />
            </div>
          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="costoMensile">Costo Mensile (€)*</Label>
            <Input
              id="costoMensile"
              type="number"
              step="0.01"
              bind:value={formData.costoMensile}
              required
              placeholder="Costo mensile"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="costoAnnuale">Costo Annuale (€)*</Label>
            <Input
              id="costoAnnuale"
              type="number"
              step="0.01"
              bind:value={formData.costoAnnuale}
              required
              placeholder="Costo annuale"
            />
          </div>

          <div class="space-y-2 sm:space-y-3 mb-2 sm:mb-0">
            <Label for="dataContratto">Data Contratto*</Label>
            <Input
              id="dataContratto"
              type="date"
              bind:value={formData.dataContratto}
              required
            />
          </div>
        </div>

        <div class="flex justify-center md:justify-end space-x-4">
          <Button type="submit" disabled={isGenerating}>
            {isGenerating ? "Generazione in corso..." : "Genera Contratto"}
          </Button>
        </div>
      </form>
    </CardContent>
  </Card>
</div>
