<script lang="ts">
  import AdminLayout from "$lib/components/ui/layout/AdminLayout.svelte";
  import { push } from "svelte-spa-router";
  import {
    Building2,
    FolderTree,
    Calendar,
    Star,
    Signature,
    Users,
  } from "lucide-svelte";
  import { authService } from "$lib/supabase/auth";
  import { toast } from "svelte-sonner";
  import { Card, CardContent, CardHeader, CardTitle } from "$lib/components/ui/card";
  import { getCurrentUserEmail } from "$lib/utils";
  import { Avatar, AvatarFallback } from "$lib/components/ui/avatar";

  const menuItems = [
    {
      name: '<PERSON><PERSON><PERSON>',
      path: '/amministrazione/comuni',
      icon: Building2,
      description: 'Gestione dei comuni e delle giunte comunali'
    },
    {
      name: 'Categorie e Servizi',
      path: '/amministrazione/categorie',
      icon: FolderTree,
      description: 'Gestione delle categorie e dei servizi'
    },
    {
      name: 'Eventi',
      path: '/amministrazione/eventi',
      icon: Calendar,
      description: 'Gestione degli eventi'
    },
    {
      name: 'In Evidenza',
      path: '/amministrazione/in-evidenza',
      icon: Star,
      description: 'Gestione degli elementi in evidenza nella Home page'
    },
    {
      name: 'Contratto di Visibilità',
      path: '/amministrazione/contratto-visibilita',
      icon: Signature,
      description: 'Gestione del contratto di visibilità'
    }
  ];

  // User statistics
  type UserStat = {
    email: string;
    created: number;
    modified: number;
  };

  let userStats = $state<UserStat[]>([]);
  let loadingStats = $state(true);
  let currentUserEmail = $state<string | null>(null);
  let showUserSection = $state(false);

  async function loadUserStats() {
    try {
      loadingStats = true;
      userStats = await authService.getUserStatistics();
    } catch (error) {
      console.error('Error loading user statistics:', error);
      toast.error("Errore nel caricamento delle statistiche utenti");
    } finally {
      loadingStats = false;
    }
  }

  async function checkUserAccess() {
    currentUserEmail = await getCurrentUserEmail();
    showUserSection = currentUserEmail === "<EMAIL>" || currentUserEmail === "<EMAIL>";
  }

  $effect(() => {
    checkUserAccess();
    loadUserStats();
  });
</script>

<AdminLayout>
  <div class="space-y-8">
    <div>
      <h1 class="text-3xl font-bold tracking-tight">Amministrazione</h1>
      <p class="text-muted-foreground">Seleziona gli oggetti da visualizzare e/o modificare</p>
    </div>

    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {#each menuItems as item}
        <button
          class="p-6 bg-card rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 text-left"
          onclick={() => push(item.path)}
        >
          <div class="flex items-center gap-4">
            <div class="p-2 bg-primary/10 rounded-lg">
              {#if item.icon === Building2}
                <Building2 class="w-6 h-6 text-primary" />
              {:else if item.icon === FolderTree}
                <FolderTree class="w-6 h-6 text-primary" />
              {:else if item.icon === Calendar}
                <Calendar class="w-6 h-6 text-primary" />
              {:else if item.icon === Star}
                <Star class="w-6 h-6 text-primary" />
              {:else if item.icon === Signature}
                <Signature class="w-6 h-6 text-primary" />
              {/if}
            </div>
            <div>
              <h3 class="font-medium text-lg">{item.name}</h3>
              <p class="text-sm text-muted-foreground">{item.description}</p>
            </div>
          </div>
        </button>
      {/each}
    </div>

    <!-- User Statistics Section - Only visible for specific users -->
    {#if showUserSection}
    <div class="mt-10">
      <div class="flex items-center gap-2 mb-4">
        <Users class="w-5 h-5" />
        <h2 class="text-2xl font-bold tracking-tight">Utenti</h2>
      </div>
      <p class="text-muted-foreground mb-6">Statistiche degli utenti amministratori</p>

      {#if loadingStats}
        <div class="text-center py-8">
          <p>Caricamento statistiche...</p>
        </div>
      {:else if userStats.length === 0}
        <div class="text-center py-8 border rounded-lg bg-muted/20">
          <p>Nessun utente amministratore trovato</p>
        </div>
      {:else}
        <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {#each userStats as user}
            <Card class="cursor-pointer hover:shadow-md transition-shadow" onclick={() => push(`/amministrazione/utenti/${encodeURIComponent(user.email)}`)}>
              <CardHeader class="pb-2">
                <CardTitle class="text-lg flex items-center gap-2">
                  <Avatar class="h-8 w-8">
                    <AvatarFallback>{user.email?.[0]?.toUpperCase()}</AvatarFallback>
                  </Avatar>
                  <span>{user.email}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div class="grid grid-cols-2 gap-2">
                  <div>
                    <p class="text-sm text-muted-foreground">Record creati</p>
                    <p class="text-2xl font-semibold">{user.created}</p>
                  </div>
                  <div>
                    <p class="text-sm text-muted-foreground">Record modificati</p>
                    <p class="text-2xl font-semibold">{user.modified}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          {/each}
        </div>
      {/if}
    </div>
    {/if}
  </div>
</AdminLayout>
