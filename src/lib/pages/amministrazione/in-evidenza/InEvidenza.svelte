<script lang="ts">
  import AdminLayout from "$lib/components/ui/layout/AdminLayout.svelte";
  import DataTable from "$lib/components/ui/data-table/DataTable.svelte";
  import EntityDialog from "$lib/components/ui/dialog/EntityDialog.svelte";
  import { Button } from "$lib/components/ui/button";
  import { CrudService } from "$lib/supabase/crud";
  import { toast } from "svelte-sonner";
  import { Edit, Trash2, ArrowLeft, Upload } from "lucide-svelte";
  import type { Tables } from "$lib/supabase/database.types";
  import { push } from "svelte-spa-router";
  import { Select, SelectTrigger, SelectContent, SelectItem } from "$lib/components/ui/select";
  import { StorageService } from '$lib/supabase/storage';
  import { Avatar, AvatarImage, AvatarFallback } from "$lib/components/ui/avatar/index.js";
  import { Dialog, DialogContent, DialogHeader, DialogTitle } from "$lib/components/ui/dialog";
  
  type InEvidenza = Tables<'in_evidenza'>;
  
  const inEvidenzaService = new CrudService('in_evidenza');
  const storageService = new StorageService('in_evidenza');
  
  let items = $state<InEvidenza[]>([]);
  let loading = $state(true);
  let dialogOpen = $state(false);
  let editingItem = $state<InEvidenza | null>(null);
  let saving = $state(false);
  let searchQuery = $state('');
  let sortField = $state<keyof InEvidenza | null>(null);
  let sortDirection = $state<'asc' | 'desc'>('asc');
  let filteredItems = $derived(getFilteredItems());
  let imageUploading = $state(false);
  let imagePreviewOpen = $state(false);
  let previewingImage = $state<string | null>(null);
  let imageTimestamps = $state<Record<string, number>>({});
  
  async function loadItems() {
    try {
      loading = true;
      items = await inEvidenzaService.getAll();
    } catch (error) {
      toast.error("Errore nel caricamento degli elementi in evidenza");
    } finally {
      loading = false;
    }
  }
  
  $effect(() => {
    loadItems();
  });
  
  function handleAdd() {
    editingItem = null;
    dialogOpen = true;
  }
  
  function handleEdit(item: InEvidenza) {
    editingItem = item;
    dialogOpen = true;
  }
  
  async function handleDelete(id: number) {
    try {
      await inEvidenzaService.delete(id);
      toast.success("Elemento eliminato con successo");
      await loadItems();
    } catch (error) {
      toast.error("Errore nell'eliminazione dell'elemento");
    }
  }

  async function handleSubmit(data: Record<string, any>) {
    try {
      saving = true;
      if (editingItem) {
        await inEvidenzaService.update(editingItem.id, data);
        toast.success("Elemento aggiornato con successo");
      } else {
        await inEvidenzaService.create(data);
        toast.success("Elemento creato con successo");
      }
      dialogOpen = false;
      await loadItems();
    } catch (error) {
      toast.error("Errore nel salvataggio dell'elemento");
    } finally {
      saving = false;
    }
  }

  function handleSearch(event: Event) {
    const target = event.target as HTMLInputElement;
    searchQuery = target.value;
  }

  function handleBack() {
    push('/amministrazione');
  }

  function handleSort(field: keyof InEvidenza) {
    if (sortField === field) {
      sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      sortField = field;
      sortDirection = 'asc';
    }
  }

  function getSortIcon(field: keyof InEvidenza) {
    if (sortField !== field) return '↕';
    return sortDirection === 'asc' ? '↑' : '↓';
  }

  function getSortOptions() {
    return [
      { value: 'titolo', label: 'Titolo' },
      { value: 'sequenza_visualizzazione', label: 'Sequenza' }
    ];
  }

  function getFilteredItems(): InEvidenza[] {
    let filtered = !searchQuery.trim() 
      ? items 
      : items.filter(item => {
          const query = searchQuery.toLowerCase();
          return (
            (item.titolo?.toLowerCase() || '').includes(query) ||
            (item.descrizione?.toLowerCase() || '').includes(query)
          );
        });
    
    if (sortField) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = sortField ? a[sortField] : null;
        const bValue = sortField ? b[sortField] : null;
        
        if (aValue === null || aValue === undefined) return sortDirection === 'asc' ? 1 : -1;
        if (bValue === null || bValue === undefined) return sortDirection === 'asc' ? -1 : 1;
        
        const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }
    
    return filtered;
  }

  async function handleImageUpload(event: Event, item: InEvidenza) {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];
    
    if (!file || !item.id) return;
    
    try {
        imageUploading = true;
        const fileName = `${item.id}.jpg`;
        await storageService.uploadFile(file, fileName);
        
        imageTimestamps[item.id.toString()] = Date.now();
        
        await loadItems();
        toast.success("Immagine caricata con successo");
    } catch (error) {
        console.error('Upload error:', error);
        toast.error("Errore nel caricamento dell'immagine");
    } finally {
        imageUploading = false;
        input.value = '';
    }
  }

  function getItemImage(item: InEvidenza) {
    if (!item?.id) return '';
    const timestamp = imageTimestamps[item.id.toString()] || '';
    const fileName = `${item.id}.jpg`;
    return storageService.getImageUrl(fileName) + (timestamp ? `?t=${timestamp}` : '');
  }

  function handleImageClick(imageUrl: string) {
    previewingImage = imageUrl;
    imagePreviewOpen = true;
  }
</script>

<AdminLayout>
  <div class="mb-4">
    <Button variant="ghost" onclick={handleBack}>
      <ArrowLeft class="mr-2 h-4 w-4" />
      Torna alla dashboard
    </Button>
  </div>

  <DataTable
    title="In Evidenza"
    description="Gestisci gli elementi in evidenza"
    onAdd={handleAdd}
    searchPlaceholder="Cerca elementi..."
    onSearch={handleSearch}
  >
    {#snippet actions()}
      <Select
        type="single"
        value={sortField || ''}
        onValueChange={(value: string) => handleSort(value as keyof InEvidenza)}
      >
        <SelectTrigger class="w-[150px] bg-transparent border-none shadow-none hover:bg-accent hover:text-accent-foreground focus:ring-0">
          {sortField 
            ? `${getSortOptions().find(opt => opt.value === sortField)?.label} ${sortDirection === 'asc' ? '↑' : '↓'}`
            : 'Ordina per...'
          }
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="">Nessun ordinamento</SelectItem>
          {#each getSortOptions() as option}
            <SelectItem value={option.value}>
              {option.label}
            </SelectItem>
          {/each}
        </SelectContent>
      </Select>
    {/snippet}

    <div class="relative overflow-x-auto">
      <!-- Mobile view (card layout) -->
      <div class="md:hidden space-y-4">
        {#if loading}
          <div class="p-4 text-center">Caricamento...</div>
        {:else if filteredItems.length === 0}
          <div class="p-4 text-center">Nessun elemento trovato</div>
        {:else}
          {#each filteredItems as item}
            <div class="bg-card rounded-lg shadow p-4 space-y-2">
              <div class="flex justify-between items-start">
                <div class="flex items-center gap-4">
                  <div class="relative">
                    <Avatar 
                      class="h-12 w-12 cursor-pointer" 
                      onclick={() => handleImageClick(getItemImage(item))}
                    >
                      <AvatarImage src={getItemImage(item)} alt={item.titolo} />
                      <AvatarFallback>{item.titolo?.[0]?.toUpperCase()}</AvatarFallback>
                    </Avatar>
                  </div>
                  <div>
                    <h3 class="font-medium">{item.titolo}</h3>
                    <p class="text-sm text-muted-foreground">Sequenza: {item.sequenza_visualizzazione}</p>
                  </div>
                </div>
                <div class="flex space-x-2">
                  <Button variant="ghost" size="icon" onclick={() => handleEdit(item)}>
                    <Edit size={16} />
                  </Button>
                  <Button variant="ghost" size="icon" onclick={() => handleDelete(item.id)}>
                    <Trash2 size={16} />
                  </Button>
                </div>
              </div>
              {#if item.descrizione}
                <p class="text-sm">{item.descrizione}</p>
              {/if}
            </div>
          {/each}
        {/if}
      </div>

      <!-- Desktop view (table layout) -->
      <table class="w-full text-sm text-left hidden md:table">
        <thead class="text-sm bg-muted">
          <tr>
            <th class="px-6 py-3">Immagine</th>
            <th class="px-6 py-3">
              <button 
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('titolo')}
              >
                <span>Titolo</span>
                <span class="text-muted-foreground">{getSortIcon('titolo')}</span>
              </button>
            </th>
            <th class="px-6 py-3">Descrizione</th>
            <th class="px-6 py-3">
              <button 
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('sequenza_visualizzazione')}
              >
                <span>Sequenza</span>
                <span class="text-muted-foreground">{getSortIcon('sequenza_visualizzazione')}</span>
              </button>
            </th>
            <th class="px-6 py-3 text-right">Azioni</th>
          </tr>
        </thead>
        <tbody>
          {#if loading}
            <tr>
              <td colspan="4" class="px-6 py-4 text-center">Caricamento...</td>
            </tr>
          {:else if filteredItems.length === 0}
            <tr>
              <td colspan="4" class="px-6 py-4 text-center">Nessun elemento trovato</td>
            </tr>
          {:else}
            {#each filteredItems as item}
              <tr class="border-b hover:bg-muted/50">
                <td class="px-6 py-4">
                  <div class="relative inline-block">
                    <Avatar 
                      class="h-12 w-12 cursor-pointer" 
                      onclick={() => handleImageClick(getItemImage(item))}
                    >
                      <AvatarImage src={getItemImage(item)} alt={item.titolo} />
                      <AvatarFallback>{item.titolo?.[0]?.toUpperCase()}</AvatarFallback>
                    </Avatar>
                  </div>
                </td>
                <td class="px-6 py-4">{item.titolo}</td>
                <td class="px-6 py-4">{item.descrizione}</td>
                <td class="px-6 py-4">{item.sequenza_visualizzazione}</td>
                <td class="px-6 py-4 text-right space-x-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleEdit(item)}
                  >
                    <Edit size={16} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleDelete(item.id)}
                  >
                    <Trash2 size={16} />
                  </Button>
                </td>
              </tr>
            {/each}
          {/if}
        </tbody>
      </table>
    </div>
  </DataTable>

  <EntityDialog
    open={dialogOpen}
    title={editingItem ? "Modifica elemento" : "Nuovo elemento"}
    description={editingItem ? "Modifica i dati dell'elemento" : "Inserisci i dati del nuovo elemento"}
    fields={[
      { key: "titolo", label: "Titolo", required: true, value: editingItem?.titolo ?? "" },
      { key: "descrizione", label: "Descrizione", type: "textarea", value: editingItem?.descrizione ?? "" },
      { key: "sequenza_visualizzazione", label: "Sequenza", type: "number", value: editingItem?.sequenza_visualizzazione ?? 0 }
    ]}
    onSubmit={handleSubmit}
    onCancel={() => dialogOpen = false}
    loading={saving}
  />

  <Dialog open={imagePreviewOpen} onOpenChange={(open) => imagePreviewOpen = open}>
    <DialogContent class="max-w-3xl">
      <DialogHeader>
        <DialogTitle>Anteprima immagine</DialogTitle>
      </DialogHeader>
      <div class="flex flex-col gap-4">
        <img src={previewingImage} alt="Preview" class="w-full rounded-lg" />
        <div class="flex justify-center">
          <div>
            <input
              type="file"
              accept="image/jpeg"
              class="hidden"
              id="imageUpload"
              onchange={(e) => {
                const item = items.find(i => getItemImage(i) === previewingImage);
                if (item) {
                  handleImageUpload(e, item);
                  imagePreviewOpen = false;
                }
              }}
              disabled={imageUploading}
            />
            <Button 
              variant="outline" 
              class="gap-2" 
              type="button"
              onclick={() => document.getElementById('imageUpload')?.click()}
            >
              <Upload class="h-4 w-4" />
              Sostituisci
            </Button>
          </div>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</AdminLayout> 