<script lang="ts">
  import AdminLayout from "$lib/components/ui/layout/AdminLayout.svelte";
  import DataTable from "$lib/components/ui/data-table/DataTable.svelte";
  import EntityDialog from "$lib/components/ui/dialog/EntityDialog.svelte";
  import { Button } from "$lib/components/ui/button";
  import { CrudService } from "$lib/supabase/crud";
  import { toast } from "svelte-sonner";
  import { Edit, Trash2, ArrowLeft, Upload } from "lucide-svelte";
  import type { Tables } from "$lib/supabase/database.types";
  import { push } from "svelte-spa-router";
  import { Select, SelectTrigger, SelectContent, SelectItem } from "$lib/components/ui/select";
  import { StorageService } from '$lib/supabase/storage';
  import { Avatar, AvatarImage, AvatarFallback } from "$lib/components/ui/avatar/index.js";
  import { Dialog, DialogContent, DialogHeader, DialogTitle } from "$lib/components/ui/dialog";

  type Evento = Tables<'eventi'>;

  const eventiService = new CrudService('eventi');
  const storageService = new StorageService('eventi');

  let eventi = $state<Evento[]>([]);
  let loading = $state(true);
  let dialogOpen = $state(false);
  let editingEvento = $state<Evento | null>(null);
  let saving = $state(false);
  let searchQuery = $state('');
  let sortField = $state<keyof Evento | null>(null);
  let sortDirection = $state<'asc' | 'desc'>('asc');
  let filteredEventi = $derived(getFilteredEventi());
  let imageUploading = $state(false);
  let imagePreviewOpen = $state(false);
  let previewingImage = $state<string | null>(null);
  let imageTimestamps = $state<Record<string, number>>({});

  async function loadEventi() {
    try {
      loading = true;
      eventi = await eventiService.getAll();
    } catch (error) {
      toast.error("Errore nel caricamento degli eventi");
    } finally {
      loading = false;
    }
  }

  $effect(() => {
    loadEventi();
  });

  function handleAdd() {
    editingEvento = null;
    dialogOpen = true;
  }

  function handleEdit(evento: Evento) {
    editingEvento = evento;
    dialogOpen = true;
  }

  async function handleDelete(id: number) {
    try {
      await eventiService.delete(id);
      toast.success("Evento eliminato con successo");
      await loadEventi();
    } catch (error) {
      toast.error("Errore nell'eliminazione dell'evento");
    }
  }

  async function handleSubmit(data: Record<string, any>) {
    try {
      saving = true;
      if (editingEvento) {
        await eventiService.update(editingEvento.id, data);
        toast.success("Evento aggiornato con successo");
      } else {
        await eventiService.create(data);
        toast.success("Evento creato con successo");
      }
      dialogOpen = false;
      await loadEventi();
    } catch (error) {
      toast.error("Errore nel salvataggio dell'evento");
    } finally {
      saving = false;
    }
  }

  function formatDate(dateString: string | null) {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('it-IT');
  }

  function formatTime(timeString: string | null) {
    if (!timeString) return '';
    return timeString.substring(0, 5); // Format HH:mm
  }

  function handleSearch(event: Event) {
    const target = event.target as HTMLInputElement;
    searchQuery = target.value;
  }

  function handleBack() {
    push('/amministrazione');
  }

  function handleSort(field: keyof Evento) {
    if (sortField === field) {
      sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      sortField = field;
      sortDirection = 'asc';
    }
  }

  function getSortIcon(field: keyof Evento) {
    if (sortField !== field) return '↕';
    return sortDirection === 'asc' ? '↑' : '↓';
  }

  function getSortOptions() {
    return [
      { value: 'titolo', label: 'Titolo' },
      { value: 'data', label: 'Data' },
      { value: 'luogo', label: 'Luogo' }
    ];
  }

  function getFilteredEventi(): Evento[] {
    let filtered = !searchQuery.trim()
      ? eventi
      : eventi.filter(evento => {
          const query = searchQuery.toLowerCase();
          return (
            (evento.titolo?.toLowerCase() || '').includes(query) ||
            (evento.luogo?.toLowerCase() || '').includes(query) ||
            (evento.descrizione?.toLowerCase() || '').includes(query)
          );
        });

    if (sortField) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = sortField ? a[sortField] : null;
        const bValue = sortField ? b[sortField] : null;

        if (aValue === null || aValue === undefined) return sortDirection === 'asc' ? 1 : -1;
        if (bValue === null || bValue === undefined) return sortDirection === 'asc' ? -1 : 1;

        const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }

    return filtered;
  }

  async function handleImageUpload(event: Event, evento: Evento) {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    if (!file || !evento.id) return;

    try {
        imageUploading = true;
        const fileName = `${evento.id}.jpg`;
        await storageService.uploadFile(file, fileName);

        imageTimestamps[evento.id.toString()] = Date.now();

        await loadEventi();
        toast.success("Immagine caricata con successo");
    } catch (error) {
        console.error('Upload error:', error);
        toast.error("Errore nel caricamento dell'immagine");
    } finally {
        imageUploading = false;
        input.value = '';
    }
  }

  function getEventImage(evento: Evento) {
    if (!evento?.id) return '';
    const timestamp = imageTimestamps[evento.id.toString()] || '';
    return storageService.getImageUrl(`${evento.id}.jpg`) + (timestamp ? `?t=${timestamp}` : '');
  }

  function handleImageClick(imageUrl: string) {
    previewingImage = imageUrl;
    imagePreviewOpen = true;
  }
</script>

<AdminLayout>
  <div class="mb-4">
    <Button variant="ghost" onclick={handleBack}>
      <ArrowLeft class="mr-2 h-4 w-4" />
      Torna alla dashboard
    </Button>
  </div>

  <DataTable
    title="Eventi"
    description="Gestisci gli eventi del territorio"
    onAdd={handleAdd}
    searchPlaceholder="Cerca eventi..."
    onSearch={handleSearch}
  >
    {#snippet actions()}
      <Select
        type="single"
        value={sortField || ''}
        onValueChange={(value: string) => handleSort(value as keyof Evento)}
      >
        <SelectTrigger class="w-[150px] bg-transparent border-none shadow-none hover:bg-accent hover:text-accent-foreground focus:ring-0">
          {sortField
            ? `${getSortOptions().find(opt => opt.value === sortField)?.label} ${sortDirection === 'asc' ? '↑' : '↓'}`
            : 'Ordina per...'
          }
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="">Nessun ordinamento</SelectItem>
          {#each getSortOptions() as option}
            <SelectItem value={option.value}>
              {option.label}
            </SelectItem>
          {/each}
        </SelectContent>
      </Select>
    {/snippet}

    <div class="relative overflow-x-auto">
      <!-- Mobile view (card layout) -->
      <div class="md:hidden space-y-4">
        {#if loading}
          <div class="p-4 text-center">Caricamento...</div>
        {:else if filteredEventi.length === 0}
          <div class="p-4 text-center">Nessun evento trovato</div>
        {:else}
          {#each filteredEventi as evento}
            <div class="bg-card rounded-lg shadow p-4 space-y-2">
              <div class="flex justify-between items-start">
                <div class="flex items-center gap-4">
                  <div class="relative">
                    <Avatar
                      class="h-12 w-12 cursor-pointer"
                      onclick={() => handleImageClick(getEventImage(evento))}
                    >
                      <AvatarImage src={getEventImage(evento)} alt={evento.titolo} />
                      <AvatarFallback>{evento.titolo?.[0]?.toUpperCase()}</AvatarFallback>
                    </Avatar>
                  </div>
                  <div>
                    <h3 class="font-medium">{evento.titolo}</h3>
                    <p class="text-sm text-muted-foreground">{formatDate(evento.data)}</p>
                  </div>
                </div>
                <div class="flex space-x-2">
                  <Button variant="ghost" size="icon" onclick={() => handleEdit(evento)}>
                    <Edit size={16} />
                  </Button>
                  <Button variant="ghost" size="icon" onclick={() => handleDelete(evento.id)}>
                    <Trash2 size={16} />
                  </Button>
                </div>
              </div>
              <div class="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span class="text-muted-foreground">Orario:</span><br />
                  {formatTime(evento.ora_inizio)} - {formatTime(evento.ora_fine)}
                </div>
                <div>
                  <span class="text-muted-foreground">Luogo:</span><br />
                  {evento.luogo}
                </div>
                <div>
                  <span class="text-muted-foreground">Premium:</span><br />
                  {evento.premium ? "Sì" : "No"}
                </div>
              </div>
            </div>
          {/each}
        {/if}
      </div>

      <!-- Desktop view (table layout) -->
      <table class="w-full text-sm text-left hidden md:table">
        <thead class="text-sm bg-muted">
          <tr>
            <th class="px-6 py-3">Immagine</th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('titolo')}
              >
                <span>Titolo</span>
                <span class="text-muted-foreground">{getSortIcon('titolo')}</span>
              </button>
            </th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('data')}
              >
                <span>Data</span>
                <span class="text-muted-foreground">{getSortIcon('data')}</span>
              </button>
            </th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('luogo')}
              >
                <span>Luogo</span>
                <span class="text-muted-foreground">{getSortIcon('luogo')}</span>
              </button>
            </th>
            <th class="px-6 py-3">Orario</th>
            <th class="px-6 py-3">Premium</th>
            <th class="px-6 py-3 text-right">Azioni</th>
          </tr>
        </thead>
        <tbody>
          {#if loading}
            <tr>
              <td colspan="6" class="px-6 py-4 text-center">Caricamento...</td>
            </tr>
          {:else if filteredEventi.length === 0}
            <tr>
              <td colspan="6" class="px-6 py-4 text-center">Nessun evento trovato</td>
            </tr>
          {:else}
            {#each filteredEventi as evento}
              <tr class="border-b hover:bg-muted/50">
                <td class="px-6 py-4">
                  <div class="relative inline-block">
                    <Avatar
                      class="h-12 w-12 cursor-pointer"
                      onclick={() => handleImageClick(getEventImage(evento))}
                    >
                      <AvatarImage src={getEventImage(evento)} alt={evento.titolo} />
                      <AvatarFallback>{evento.titolo?.[0]?.toUpperCase()}</AvatarFallback>
                    </Avatar>
                  </div>
                </td>
                <td class="px-6 py-4">{evento.titolo}</td>
                <td class="px-6 py-4">{formatDate(evento.data)}</td>
                <td class="px-6 py-4">
                  {formatTime(evento.ora_inizio)} - {formatTime(evento.ora_fine)}
                </td>
                <td class="px-6 py-4">{evento.luogo}</td>
                <td class="px-6 py-4">
                  {evento.premium ? "Sì" : "No"}
                </td>
                <td class="px-6 py-4 text-right space-x-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleEdit(evento)}
                  >
                    <Edit size={16} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleDelete(evento.id)}
                  >
                    <Trash2 size={16} />
                  </Button>
                </td>
              </tr>
            {/each}
          {/if}
        </tbody>
      </table>
    </div>
  </DataTable>

  <EntityDialog
    open={dialogOpen}
    title={editingEvento ? "Modifica evento" : "Nuovo evento"}
    description={editingEvento ? "Modifica i dati dell'evento" : "Inserisci i dati del nuovo evento"}
    fields={[
      { key: "titolo", label: "Titolo", required: true, value: editingEvento?.titolo ?? "" },
      { key: "descrizione", label: "Descrizione", value: editingEvento?.descrizione ?? "" },
      { key: "data", label: "Data", type: "date", required: true, value: editingEvento?.data ?? "" },
      { key: "ora_inizio", label: "Ora inizio", type: "time", required: true, value: editingEvento?.ora_inizio ?? "" },
      { key: "ora_fine", label: "Ora fine", type: "time", required: true, value: editingEvento?.ora_fine ?? "" },
      { key: "luogo", label: "Luogo", required: true, value: editingEvento?.luogo ?? "" },
      { key: "comune_id", label: "Comune", type: "number", required: true, value: editingEvento?.comune_id ?? "" },
      { key: "premium", label: "Premium", type: "checkbox", value: editingEvento?.premium ?? false },
      { key: "in_evidenza_id", label: "In evidenza", type: "number", value: editingEvento?.in_evidenza_id ?? null }
    ]}
    onSubmit={handleSubmit}
    onCancel={() => dialogOpen = false}
    loading={saving}
  />

  <Dialog open={imagePreviewOpen} onOpenChange={(open) => imagePreviewOpen = open}>
    <DialogContent class="max-w-3xl">
      <DialogHeader>
        <DialogTitle>Anteprima immagine</DialogTitle>
      </DialogHeader>
      <div class="flex flex-col gap-4">
        <img src={previewingImage} alt="Preview" class="w-full rounded-lg" />
        <div class="flex justify-center">
          <div>
            <input
              type="file"
              accept="image/jpeg"
              class="hidden"
              id="imageUpload"
              onchange={(e) => {
                const evento = eventi.find(ev => getEventImage(ev) === previewingImage);
                if (evento) {
                  handleImageUpload(e, evento);
                  imagePreviewOpen = false;
                }
              }}
              disabled={imageUploading}
            />
            <Button
              variant="outline"
              class="gap-2"
              type="button"
              onclick={() => document.getElementById('imageUpload')?.click()}
            >
              <Upload class="h-4 w-4" />
              Sostituisci
            </Button>
          </div>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</AdminLayout>