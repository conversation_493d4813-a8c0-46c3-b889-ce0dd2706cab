{"name": "il-salento-web", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.json && tsc -p tsconfig.node.json"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^5.0.0", "@tsconfig/svelte": "^5.0.4", "@types/file-saver": "^2.0.7", "@types/node": "^22.10.2", "autoprefixer": "^10.4.20", "bits-ui": "^1.3.19", "lucide-svelte": "^0.471.0", "mode-watcher": "^0.5.0", "svelte": "^5.0.0-next.272", "svelte-check": "^4.1.0", "svelte-sonner": "^0.3.28", "tailwindcss": "^3.4.9", "tslib": "^2.8.1", "typescript": "~5.6.2", "vite": "^6.0.1"}, "dependencies": {"@supabase/supabase-js": "^2.47.3", "clsx": "^2.1.1", "docxtemplater": "^3.42.4", "file-saver": "^2.0.5", "pdf-lib": "^1.17.1", "pizzip": "^3.1.4", "svelte-spa-router": "^4.0.1", "tailwind-merge": "^2.5.5", "tailwind-variants": "^0.3.0", "tailwindcss-animate": "^1.0.7"}}